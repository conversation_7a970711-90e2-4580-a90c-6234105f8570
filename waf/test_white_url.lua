

-- 测试白名单URL优化功能
-- 模拟OpenResty环境
-- 执行命令：lua test_white_url.lua

-- 模拟ngx模块
local ngx = {
    log = function(level, ...)
        print("LOG:", ...)
    end,
    INFO = "INFO",
    var = {
        request_uri = "/api/health"  -- 测试用的请求URI
    },
    re = {
        find = function(subject, pattern, options)
            -- 简化的正则匹配模拟，专门处理我们的测试用例
            if pattern == "^/admin/.*" then
                return string.find(subject, "^/admin/") and 1 or nil
            elseif pattern == "^/public/.*%.css$" then
                return string.find(subject, "^/public/.*%.css$") and 1 or nil
            elseif pattern == "^/public/.*%.js$" then
                return string.find(subject, "^/public/.*%.js$") and 1 or nil
            elseif pattern == "^/public/.*%.png$" then
                return string.find(subject, "^/public/.*%.png$") and 1 or nil
            elseif pattern == "^/user/unionUser/keyInfo/encrypt" then
                return string.find(subject, "^/user/unionUser/keyInfo/encrypt") and 1 or nil
            else
                -- 普通匹配
                return string.find(subject, pattern) and 1 or nil
            end
        end
    }
}

-- 模拟配置
config_white_url_check = "on"
config_rule_dir = "./rule-config"

-- 模拟get_rule函数
function get_rule(filename)
    local file = io.open(config_rule_dir .. "/" .. filename, "r")
    if not file then
        return nil
    end
    
    local rules = {}
    for line in file:lines() do
        if line ~= "" then
            table.insert(rules, line)
        end
    end
    file:close()
    return rules
end

-- 引入我们优化后的代码逻辑
local white_url_exact_table = {}
local white_url_regex_table = {}

function init_white_url_rules()
    if config_white_url_check == "on" then
        local URL_WHITE_RULES = get_rule('whiteurl.rule')
        if URL_WHITE_RULES ~= nil then
            for _, rule in pairs(URL_WHITE_RULES) do
                if rule ~= "" then
                    -- 判断是否为正则表达式（包含特殊字符）
                    if string.find(rule, "[%^%$%(%)%[%]%*%+%?%|%\\%.]") then
                        -- 正则表达式规则
                        table.insert(white_url_regex_table, rule)
                    else
                        -- 精确匹配规则，使用table作为hash表实现O(1)查询
                        white_url_exact_table[rule] = true
                    end
                end
            end
            -- 计算精确匹配规则数量
            local exact_count = 0
            for _ in pairs(white_url_exact_table) do
                exact_count = exact_count + 1
            end
            ngx.log(ngx.INFO, "White URL rules loaded: ", #white_url_regex_table, " regex rules, ", 
                    exact_count, " exact rules")
        end
    end
end

function white_url_check()
    if config_white_url_check == "on" then
        local REQ_URI = ngx.var.request_uri
        
        -- 首先进行O(1)精确匹配检查
        if white_url_exact_table[REQ_URI] then
            return true
        end
        
        -- 如果精确匹配失败，再进行正则匹配
        for _, rule in pairs(white_url_regex_table) do
            if ngx.re.find(REQ_URI, rule, "jo") then
                return true
            end
        end
    end
    return false
end

-- 测试函数
function test_white_url_optimization()
    print("=== 测试白名单URL优化功能 ===")
    
    -- 初始化规则
    init_white_url_rules()
    
    -- 测试用例
    local test_cases = {
        "/api/health/xxx",           -- 精确匹配
        "/api/status",           -- 精确匹配
        "/exact/match/url",      -- 精确匹配
        "/admin/dashboard",      -- 正则匹配 ^/admin/.*
        "/public/style.css",     -- 正则匹配 ^/public/.*\.css$
        "/public/script.js",     -- 正则匹配 ^/public/.*\.js$
        "/user/unionUser/keyInfo/encrypt/test", -- 正则匹配 ^/user/unionUser/keyInfo/encrypt
        "/forbidden/url",        -- 应该被拒绝
        "/another/forbidden"     -- 应该被拒绝
    }
    
    for _, test_uri in pairs(test_cases) do
        ngx.var.request_uri = test_uri
        local result = white_url_check()
        print(string.format("URI: %-35s Result: %s", test_uri, result and "ALLOWED" or "DENIED"))
    end
    
    print("\n=== 性能对比测试 ===")
    
    -- 模拟旧版本的实现（每次都读取文件）
    function old_white_url_check()
        if config_white_url_check == "on" then
            local URL_WHITE_RULES = get_rule('whiteurl.rule')
            local REQ_URI = ngx.var.request_uri
            if URL_WHITE_RULES ~= nil then
                for _,rule in pairs(URL_WHITE_RULES) do
                    if rule ~= "" and ngx.re.find(REQ_URI,rule,"jo") then
                        return true
                    end
                end
            end
        end
        return false
    end
    
    -- 性能测试
    local test_uri = "/api/health"
    ngx.var.request_uri = test_uri
    
    local iterations = 10000
    
    -- 测试新版本
    local start_time = os.clock()
    for i = 1, iterations do
        white_url_check()
    end
    local new_time = os.clock() - start_time
    
    -- 测试旧版本
    start_time = os.clock()
    for i = 1, iterations do
        old_white_url_check()
    end
    local old_time = os.clock() - start_time
    
    print(string.format("新版本 %d 次调用耗时: %.4f 秒", iterations, new_time))
    print(string.format("旧版本 %d 次调用耗时: %.4f 秒", iterations, old_time))
    print(string.format("性能提升: %.2fx", old_time / new_time))
end

-- 运行测试
test_white_url_optimization()
