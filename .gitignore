
.metadata
bin/
tmp/
*.tmp
*.bak
*.swp
*~.nib
local.properties
.settings/
.loadpath
.recommenders
.DS_Store

# External tool builders
.externalToolBuilders/

# Locally stored "Eclipse launch configurations"
*.launch

# PyDev specific (Python IDE for Eclipse)
*.pydevproject

# CDT-specific (C/C++ Development Tooling)
.cproject

# Java annotation processor (APT)
.factorypath

# PDT-specific (PHP Development Tools)
.buildpath

# sbteclipse plugin
.target

# Tern plugin
.tern-project

# TeXlipse plugin
.texlipse

# STS (Spring Tool Suite)
.springBeans

# Code Recommenders
.recommenders/

# Scala IDE specific (Scala & Java development for Eclipse)
.cache-main
.scala_dependencies
.worksheet
# built application files
*.apk
*.ap_

# files for the dex VM
*.dex

# generated files
/bin/
/gen/

# Local configuration file (sdk path, etc)
local.properties

# Eclipse project files
.classpath
.project
.settings/

# Proguard folder generated by Eclipse
proguard/


# Maven
/log/
target/
/target/

# Package Files #
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Compiled class file
*.class

# Log file
*.log

### Gradle Files ###
.gradle
/build/
!gradle/wrapper/gradle-wrapper.jar

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
/out/
.idea/workspace.xml
.idea/*
