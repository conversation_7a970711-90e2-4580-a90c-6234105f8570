package com.gsms.internet.gateway.util;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/**
 * md5加密处理工具类
 * <AUTHOR>
 * @date 2023/10/20 14:55
 * @company 昆仑数智科技有限责任公司
 */
public class MD5Utils {

   /* public static void main(String[] args) {
        String str = "{\"longitude\":115.28406312830671,\"latitude\":40.967686610534976,\"pageNum\":1,\"pageSize\":10,\"distance\":\"10\",\"bookingRefueling\":\"\",\"mapType\":\"1\",\"orgCode\":\"\"}";
        String ey = "jhnejrjrjUUUmmd33";
        String time = "1676341906";
        System.out.println(MD5Utils.MD5(str + ey + time));
    }*/

    public static String MD5(String content) {
        MessageDigest md = null;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            return "";
        }
        byte[] array = md.digest(content.getBytes(StandardCharsets.UTF_8));
        StringBuilder sb = new StringBuilder();
        for (byte item : array) {
            sb.append(Integer.toHexString((item & 0xFF) | 0x100), 1, 3);
        }
        return sb.toString();
    }

}
