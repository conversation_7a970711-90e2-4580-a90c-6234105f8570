package com.gsms.internet.gateway.enums;

import java.util.Objects;

/**
 * 网关校验类异常提示信息汇总枚举类
 */
public enum ErrorCodeEnum {
    FAILED("S_C20_000000", "系统异常"),
    CUSTOM_ERROR("B_C20_999999", "请求失败,请尝试进行重试操作"),
    HTTP_BODY_ERROR("B_C20_000007", "请求体解析失败"),
    REQUEST_ERROR("B_C20_000006", "请求权限校验未通过"),
    TOKEN_EXPIRED("B_C20_000008", "访问凭证过期"),
    AUTH_ERROR("B_C20_000005", "访问凭证失效,请重新登录"),
    RSA_VALID_ERROR("B_C20_000012", "校验未通过,请尝试重新进行该流程"),
    AUTH_CLIENT_ERROR("B_C20_000011", "未授权该渠道访问"),
    TIME_ERROR("B_C20_000004", "可能长时间未使用,请您点击“确定”后重新加载页面"),
    SIGN_ERROR("B_C20_000003", "客户端签名验证未通过,请升级客户端版本到最新"),
    RATE_LIMIT_ERROR("B_C20_000099", "请求过于频繁"),
    PARAM_ERROR("P_C20_000001", "客户端参数错误,请升级客户端版本到最新"),
    CLIENT_CODE_ERROR("B_C20_000001", "请求权限校验未通过"),
    SMS_CODE_ERROR("B_C20_000009", "验证码错误，请重新输入"),
    SMS_CODE_EXPIRE("B_C20_000010", "验证码过期，请重新获取"),
    HARMONY_PUB_KEY_ERROR("B_C20_000088", "生成设备公钥失败"),
    HARMONY_PUB_KEY_NOT_EXISTS("B_C20_000089", "设备公钥不存在,请尝试重新获取"),
    AUTH_LOGIN_FAIL("B_C20_000002", "当前用户不存在"),
	FAILD_CALLBACK_ERROR("B_C20_999001", "访问人数过多，请稍后再试");

    // 异常码
    private final String code;
    // 异常提示信息
    private final String desc;

    ErrorCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return this.code;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return "ErrorCodeEnum{" + "code='" + code + '\'' + ", desc='" + desc + '\'' + '}';
    }

    /**
     * 根据code查找Enum
     *
     * @param code
     * @return
     */
    public static ErrorCodeEnum getName(String code) {
        ErrorCodeEnum[] allEnums = values();
        for (ErrorCodeEnum allEnum : allEnums) {
            if (Objects.equals(allEnum.getCode(), code)) {
                return allEnum;
            }
        }
        return null;
    }

}
