package com.gsms.internet.gateway.config;

import com.gsms.internet.gateway.bean.ClientCode;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@Component
@RefreshScope
@ConfigurationProperties(prefix = "system.department.key")
public class ClientCodeConfig {

    private Map<String, ClientCode> configs;

}