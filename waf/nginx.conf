user  nobody;
worker_processes  1;
error_log  /logs/nginx.error.log info;

events {
    use epoll;
    worker_connections  5000;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    log_format  main  '[$remote_addr] [$http_x_forwarded_for] [$status] [$upstream_status] [$http_clientuuid] - [$time_local] "$request" '
                      '$upstream_connect_time->$upstream_response_time->$request_time '
                      '"$http_user_agent" "$body_bytes_sent" "$upstream_addr"';

    sendfile        on;
    keepalive_timeout  0;
    server_tokens off;

    # 指定lua脚本目录
    lua_package_path "/usr/local/openresty/waf/?.lua;;";

    # 配置共享内存用于存储WAF规则和缓存
    lua_shared_dict limit 10m;
    lua_shared_dict mycache 10m;

    # 在OpenResty启动时初始化白名单规则
    init_by_lua_file /usr/local/openresty/waf/init.lua;

    server {
        listen 8080;
#        root /usr/share/nginx/html/build/h5;
        underscores_in_headers on;
        server_name  localhost;

        if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})") {
            set $year $1;
            set $month $2;
            set $day $3;
        }

        if ($http_operation_type != "") {
            set $operation_type "${http_operation_type}";
        }

        access_log /logs/access_$year-$month-$day.log main;

        # 访问核心区clb
        location /ees/ {
            access_by_lua_file /usr/local/openresty/waf/accesscontroll.lua;
            proxy_ignore_client_abort on;
            proxy_intercept_errors on;
            proxy_pass http://************:30304;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            set_real_ip_from **********/18;
            real_ip_header X-Forwarded-For;
        }

        # 静态页面
#         location /h5 {
#             alias   /usr/share/nginx/html/build/h5;
#             try_files $uri $uri/ /index.html =404;
#         }

        location / {
            if ($request_method = HEAD) {
                access_log off;
            }
        }

        location = /health {
            access_log off;
            return 200 'OK';
        }

        location /.well-known/apple-app-site-association {
            charset UTF-8;
            default_type text/html;
            return 200 '{"applinking":{"apps":[{"appIdentifier": "5765880207855376869"}]}}';
        }
    }
}