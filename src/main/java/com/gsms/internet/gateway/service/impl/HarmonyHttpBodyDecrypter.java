package com.gsms.internet.gateway.service.impl;

import com.gsms.internet.gateway.bean.HKYZHttpBody;
import com.gsms.internet.gateway.service.HttpBodyDecrypter;
import com.gsms.internet.gateway.util.AesUtils;
import com.gsms.internet.gateway.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 鸿蒙os的解密器
 *
 * <AUTHOR>
 * @date 2024/8/7 09:36
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class HarmonyHttpBodyDecrypter implements HttpBodyDecrypter {

    @Value("${harmony.workKey:}")
    private String workKey; // aes算法的密钥，用于真正加密http-body

    public HKYZHttpBody decrypt(String body, String clientCode, String methodName) throws Exception {
        HKYZHttpBody hkyzHttpBody = new HKYZHttpBody();
        String signBody = JSONUtils.parse(body).get("jsonData").asText();
        hkyzHttpBody.setSignBody(signBody);
        // 获取方法名
        String newMethodName = methodName;
        if (newMethodName.contains("?")) {
        	newMethodName = newMethodName.substring(0, newMethodName.indexOf("?"));
        }
        if("/secret/getImportKey".equals(newMethodName) || "/secret/getWorkKey".equals(newMethodName)) {
        	hkyzHttpBody.setBody(signBody);
        } else {
        	hkyzHttpBody.setBody(AesUtils.decrypt(signBody, workKey));
        }
        return hkyzHttpBody;
    }
}
