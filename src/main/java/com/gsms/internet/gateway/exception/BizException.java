package com.gsms.internet.gateway.exception;

import com.gsms.internet.gateway.dto.ResponseDTO;
import com.gsms.internet.gateway.enums.*;

/**
 * 自定义业务异常
 *
 * <AUTHOR>
 */
public class BizException extends RuntimeException {

    private final String code;

    public BizException() {
        super();
        code = ErrorCodeEnum.FAILED.getCode();
    }

    public BizException(String message) {
        super(message);
        code = ErrorCodeEnum.FAILED.getCode();
    }

    public BizException(ResponseDTO<?> responseDTO) {
        super(responseDTO.getMessage());
        this.code = responseDTO.getErrorCode();
    }

    public BizException(String code, String message) {
        super(message);
        this.code = code;
    }

    public BizException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getDesc());
        this.code = errorCodeEnum.getCode();
    }


    public BizException(String message, Throwable cause) {
        super(message, cause);
        code = ErrorCodeEnum.FAILED.getCode();
    }

    public BizException(Throwable cause) {
        super(cause);
        code = ErrorCodeEnum.FAILED.getCode();
    }

    public String getCode() {
        return code;
    }

}
