-- openresty access_by_lua_file 配置
require 'init'

function waf_main()
    log_kafka()
    if white_ip_check() then
    elseif white_url_check() then
    elseif black_ip_check() then
    elseif autofilterip() then
    elseif tokencheck() then
    elseif user_agent_attack_check() then
    elseif cc_attack_check() then
    elseif blackallpathcheck() then
    elseif cookie_attack_check() then
    elseif url_attack_check() then
    elseif url_args_attack_check() then
    --elseif post_attack_check() then
    else
        getTpagedata()
        return
    end
end

waf_main()

