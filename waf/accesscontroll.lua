-- 引入相关依赖包
require 'init'

-- 动态配置：需要剥离的代理前缀列表
local PROXY_PREFIXES = {
    "/ees/",
    "/ees-dev/",
    "/ees-qas/",
    "/ees-gray/"
}


-- 校验header
function headers_check()
    return true
end

-- 获取当前请求的代理前缀配置
function get_proxy_prefixes()
    return PROXY_PREFIXES
end

function access()
    -- 获取动态配置
    local proxy_prefixes = get_proxy_prefixes()

    -- 调用白名单检查，传入动态配置
    if white_url_check(proxy_prefixes) then
    else
        returnErrorJson("访问控制【白名单限制】,拒绝访问");
        return ngx.exit(200);
    end

    if headers_check() then
    else
        returnErrorJson("访问控制【参数限制】,拒绝访问");
        return ngx.exit(200);
    end
end

access()