server:
  port: 80
  cce: true

jasypt:
  encryptor:
    password: ${JASYPT_KEY:ZYSO96CMPZDUUKLVSLO2GW7PL}
    algorithm: PBEWithMD5AndDES
    iv-generator-classname: org.jasypt.iv.NoIvGenerator
    property:
      prefix: ENC(
      suffix: )

spring:
  application:
    name: control-internet
  cloud:
    nacos:
      discovery:
        enabled: true
        server-addr: ${PAAS_NACOS_SC_ENDPOINT}
        service: ${spring.application.name}
        namespace: ${PAAS_NACOS_SC_NAMESPACE}
        group: ${PAAS_NACOS_SC_GROUP}
        heart-beat-interval: 2000 # nacos心跳间隔
        heart-beat-timeout: 4000 # 多长时间之后未收到心跳认为实例不健康
        ip-delete-timeout: 6000 # 多长时间未收到心跳之后直接删除实例
      config:
        enabled: true
        server-addr: ${PAAS_NACOS_CC_ENDPOINT}
        namespace: ${PAAS_NACOS_CC_NAMESPACE}
        file-extension: yml
        shared-configs:
          - data-id: common.yml
            group: ${PAAS_NACOS_CC_GROUP}
            refresh: true
          - data-id: redis.yml
            group: ${PAAS_NACOS_CC_GROUP}
            refresh: true
        extension-configs:
          - data-id: error-code.yml
            group: ${PAAS_NACOS_CC_GROUP}
            refresh: true
          - data-id: harmony.yml
            group: ${PAAS_NACOS_CC_GROUP:hkyz}
            refresh: true
          - data-id: client-code.yml
            group: ${PAAS_NACOS_CC_GROUP:hkyz}
            refresh: true
        group: ${PAAS_NACOS_CC_GROUP}


management:
  metrics:
    tags:
      application: ${spring.application.name}






