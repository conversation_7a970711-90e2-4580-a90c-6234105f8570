package com.gsms.internet.gateway.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.gsms.internet.gateway.bean.UserInfoBo;
import com.gsms.internet.gateway.dto.AuthDTO;
import com.gsms.internet.gateway.exception.BizException;
import com.gsms.internet.gateway.exception.TokenExpiredException;
import com.gsms.internet.gateway.service.TokenValidator;
import com.gsms.internet.gateway.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 验证token抽象类
 * 使用jwt-token
 *
 * <AUTHOR>
 * @date 2022/10/17 14:41
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Component
@RefreshScope
public class TokenValidatorImpl implements TokenValidator {

    private static final String KEY_BLACK_PREFIX = "BLACK_USER_";

    @Value("${token.secret}")
    private String secret;

    @Value("${token.author}")
    private String author;

    @Autowired
    private RedisUtils redisUtils;

    /**
     * 校验token并返回用户信息
     *
     * <AUTHOR>
     * @date 2023/10/18 09:46
     * @company 昆仑数智科技有限责任公司
     */
    @Override
    public UserInfoBo validTokenAndGetUserInfo(String token, String clientCode) throws TokenExpiredException {
        UserInfoBo userInfoBo = new UserInfoBo();
        // 1.校验空
        if (StringUtils.isBlank(token) || StringUtils.isBlank(clientCode)) {
            log.info("参数不全,未传token或者渠道号");
            throw new BizException("参数不全");
        }

        // 2.解密
        String whereTypeDecode = "";
        String userId = "";
        String authorization = "";
        boolean temporary = false;
        Long expire = null;
        try {
            JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(secret)).withIssuer(author).build();
            DecodedJWT decodedJWT = jwtVerifier.verify(token);
            whereTypeDecode = decodedJWT.getClaim("clientCode").asString();
            userId = decodedJWT.getClaim("userId").asString();

            // 以下处理临时token特殊字段
            if (!decodedJWT.getClaim("temporary").isNull()) {
                temporary = decodedJWT.getClaim("temporary").asBoolean();
            }
            if (!decodedJWT.getClaim("authorization").isNull()) {
                authorization = decodedJWT.getClaim("authorization").asString();
            }
            if (!decodedJWT.getClaim("expire").isNull()) {
                expire = decodedJWT.getClaim("expire").asLong();
            }
        } catch (IllegalArgumentException | JWTVerificationException e) {
            log.info("token解密失败");
            throw new BizException("token错误");
        }

        // 3.相关校验
        AuthDTO authDTO = valid(clientCode, whereTypeDecode, token, userId, temporary);
        userInfoBo.setUserId(userId);
        userInfoBo.setAuthDTO(authDTO);

        // 4.对于临时token的特殊处理
        if (temporary) {
            if (StringUtils.isBlank(authorization)) {
                log.error("临时token的authorization字段为空");
                throw new BizException("token错误");
            }
            if (expire == null || System.currentTimeMillis() > expire) {
                log.error("临时token已过期");
                throw new BizException("token错误");
            }
            log.info("该token属于临时token,token校验通过");
            userInfoBo.getAuthDTO().setAuthorization(authorization);
        }
        return userInfoBo;
    }

    private AuthDTO valid(String whereType, String whereTypeDecode, String token, String userId, boolean temporary) throws TokenExpiredException {
        if (StringUtils.isBlank(whereTypeDecode)) {
            log.info("获取渠道号失败");
            throw new BizException("token错误");
        }
        if (!whereType.equals(whereTypeDecode)) {
            log.info("传入渠道号{}和解密渠道号{}不一致", whereType, whereTypeDecode);
            throw new BizException("token错误");
        }
        String blackKey = KEY_BLACK_PREFIX + userId;
        if (redisUtils.hasKey(blackKey)) {
            log.info("用户{}在黑名单,进行拦截", userId);
            throw new BizException("用户在名单,不可访问系统");
        }
        if (temporary) {
            return new AuthDTO();
        }
        String key = getTokenKey(userId, whereType);
        AuthDTO authDTO = redisUtils.get(key, AuthDTO.class);
        if (authDTO != null) {
            if (!authDTO.getToken().equals(token)) {
                log.info("传入的token和redis中存储的token不一致");
                throw new BizException("token错误");
            }
        } else {
            throw new TokenExpiredException(userId + "&" + whereTypeDecode);
        }
        return authDTO;
    }

    private String getTokenKey(String userId, String clientCode) {
        return "token:" + userId + "_" + clientCode;
    }

}
