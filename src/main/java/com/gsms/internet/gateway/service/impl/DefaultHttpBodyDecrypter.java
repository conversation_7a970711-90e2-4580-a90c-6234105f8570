package com.gsms.internet.gateway.service.impl;

import com.gsms.internet.gateway.bean.HKYZHttpBody;
import com.gsms.internet.gateway.service.HttpBodyDecrypter;
import com.sunbox.BangBangAESUtil;
import com.gsms.internet.gateway.enums.ClientCodeEnum;
import com.gsms.internet.gateway.enums.ErrorCodeEnum;
import com.gsms.internet.gateway.exception.BizException;
import com.gsms.internet.gateway.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 默认的解密器
 *
 * <AUTHOR>
 * @date 2024/8/7 09:36
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class DefaultHttpBodyDecrypter implements HttpBodyDecrypter {

    @Value("${spring.profiles.active}")
    private String env;

    public HKYZHttpBody decrypt(String body, String clientCode, String methodName) throws Exception {
        HKYZHttpBody hkyzHttpBody = new HKYZHttpBody();
        String signBody = body;
        if (clientCode.equals(ClientCodeEnum.APP.getClientCode())) {
            signBody = JSONUtils.parse(body).get("jsonData").asText();
            if (!"dev".equals(env)) {
                if (signBody.startsWith("F")) {
                    log.error("当前客户端使用旧版密钥，已经阻断请求");
                    throw new BizException(ErrorCodeEnum.REQUEST_ERROR);
                }
                if ("prd".equals(env)) {
                    body = BangBangAESUtil.decryptPro(signBody);
                } else {
                    body = BangBangAESUtil.decrypt(signBody);
                }
                // 这里主要是对body做二次安全验证
                if (BangBangAESUtil.Check_And_ck(body) == 0) {
                    log.error("app请求体验签失败");
                    throw new BizException(ErrorCodeEnum.HTTP_BODY_ERROR);
                }
            } else {
                body = signBody;
            }
        }
        hkyzHttpBody.setBody(body);
        hkyzHttpBody.setSignBody(signBody);
        return hkyzHttpBody;
    }

}
