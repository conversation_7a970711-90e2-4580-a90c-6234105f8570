<?xml version="1.0" encoding="UTF-8"?>
<!-- 日志级别从低到高分为TRACE < DEBUG < INFO < WARN < ERROR < FATAL，如果设置为WARN，则低于WARN的信息都不会输出 -->
<!-- scan:当此属性设置为true时，配置文档如果发生改变，将会被重新加载，默认值为true -->
<!-- scanPeriod:设置监测配置文档是否有修改的时间间隔，如果没有给出时间单位，默认单位是毫秒。
                 当scan为true时，此属性生效。默认的时间间隔为1分钟。 -->
<!-- debug:当此属性设置为true时，将打印出logback内部日志信息，实时查看logback运行状态。默认值为false。 -->
<configuration>
    <contextName>logback</contextName>
    <!--应用名appName-->
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="profile" source="spring.profiles.active"/>
    <springProperty scope="context" name="cce" source="server.cce"/>


    <!--1. 输出到控制台,用于本地启动-->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!--此日志appender是为开发使用，只配置最底级别，控制台输出的日志级别是大于或等于此级别的日志信息-->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}[%X{userId:-},%X{UUID:-},%X{ori:-}] [%thread] %-5level %logger{50} - %msg%n
            </pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!--docker 标准输出,用于k8s部署-->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>info</level>
        </filter>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai} [%X{userId:-},%X{UUID:-},%X{ori:-}] [%thread] %-5level %logger{50} - %msg%n
            </pattern>
            <charset>UTF-8</charset>
            <!-- 禁用即时刷新提高日志吞吐量 -->
            <immediateFlush>false</immediateFlush>
        </encoder>
    </appender>


    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <appender-ref ref="STDOUT"/>
        <neverBlock>true</neverBlock>
        <queueSize>2048</queueSize>
    </appender>

    <!--非生产环境-->
    <springProfile name="qas,dev,sit,uat,gray,ben,devlocal">
        <root level="INFO">
            <if condition='property("cce").contains("false")'>
                <then>
                    <!-- 控制台输出 -->
                    <appender-ref ref="CONSOLE"/>
                </then>
                <else>
                    <!-- docker标准输出 -->
                    <appender-ref ref="STDOUT"/>
                </else>
            </if>
        </root>
    </springProfile>


    <!-- 生产+预生产环境. -->
    <springProfile name="prd,pre">
        <root level="INFO">
            <appender-ref ref="ASYNC"/>
        </root>
    </springProfile>

    <!-- 压测环境. -->
    <springProfile name="stress">
        <root level="INFO">
            <appender-ref ref="ASYNC"/>
        </root>
    </springProfile>

</configuration>