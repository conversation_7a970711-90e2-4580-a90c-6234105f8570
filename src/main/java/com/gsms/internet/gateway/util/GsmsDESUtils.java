package com.gsms.internet.gateway.util;

import com.gsms.internet.gateway.exception.BizException;
import org.apache.commons.codec.binary.Base64;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


public class GsmsDESUtils {

    //默认为 DESede/ECB/PKCS5Padding
    private static final String CIPHER_TRANSFORMAT = "TripleDES/ECB/PKCS5Padding";
    private static final String ALGORITHM = "TripleDES";

    /**
     * 直接数组编码方式，"GB2312"
     */
    private static final String ENCODING = "GB2312";

    /**
     * 密钥key，需要改成从配置文件读取，以便后续修改
     */
    private static final String key = "qwerQ12Wdts";

    private static final char[] HEX_CHAR = {'0', '1', '2', '3', '4', '5',
            '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    /**
     * 加密类型方式(0:95504, 1:B2B加密方式)
     */
    private static final Integer ENCRYPT_TYPE = 0;

    public static void main(String[] args) {
        try {
            String str = encryptDES("{\"orderId\":2,\"str\":\"哈哈\"}", "oommmkkjeennqw99732!!@#$##");
            // 加密
            System.out.println("加密后密文:" + str);

            // 解密
            System.out.println("解密后字符串：" + decryptFromDES(str, "oommmkkjeennqw99732!!@#$##"));

        } catch (Exception e) {
        	throw new BizException("算法错误", e);
        }
    }

    /**
     * 获取字符串3DES加密结果，密钥先用MD5加密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encryptToBase64(String plainText) throws Exception {

        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(key), ALGORITHM);
        Cipher c1 = Cipher.getInstance(CIPHER_TRANSFORMAT);
        c1.init(Cipher.ENCRYPT_MODE, md5Key);
        byte[] result = c1.doFinal(plainText.getBytes(ENCODING));

        return Base64.encodeBase64String(result);
    }

    /**
     * 获取字符串3DES加密结果，密钥先用MD5加密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encryptDES(String plainText, String enckey) throws Exception {

        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(enckey), ALGORITHM);
        Cipher c1 = Cipher.getInstance(CIPHER_TRANSFORMAT);
        c1.init(Cipher.ENCRYPT_MODE, md5Key);
        byte[] result = c1.doFinal(plainText.getBytes(ENCODING));

        return Base64.encodeBase64String(result);
    }

    /**
     * 2次MD5加密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String encryptBy2Md5(String plainText) throws Exception {
        if (0 == ENCRYPT_TYPE) {
            return encryptToBase64(plainText);
        }
        //确定计算方法
        MessageDigest md5 = null;
        String newstr = null;
        try {
            md5 = MessageDigest.getInstance("MD5");
            ;
            String firstString = bytesToHexFun2(md5.digest(plainText.getBytes(StandardCharsets.UTF_8)));
            BASE64Encoder base64en = new BASE64Encoder();
            //加密后的字符串
            newstr = base64en.encode(md5.digest(firstString.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException e) {
        	throw new BizException("算法错误", e);
        }
        return newstr;
    }


    /**
     * 将3DES加密结果的base64字符串进行解密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String decryptFromBase64String(String plainText) throws Exception {

        byte[] data = Base64.decodeBase64(plainText.getBytes());
        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(key), ALGORITHM);
        Cipher c1 = Cipher.getInstance(CIPHER_TRANSFORMAT);
        c1.init(Cipher.DECRYPT_MODE, md5Key);
        byte[] result = c1.doFinal(data);
        return new String(result, ENCODING);
    }


    public static String decryptFromDES(String plainText, String key) throws Exception {

        byte[] data = Base64.decodeBase64(plainText.getBytes());
        SecretKey md5Key = new SecretKeySpec(keyMD5Encode(key), ALGORITHM);
        Cipher c1 = Cipher.getInstance(CIPHER_TRANSFORMAT);
        c1.init(Cipher.DECRYPT_MODE, md5Key);
        byte[] result = c1.doFinal(data);
        return new String(result, ENCODING);
    }

    /**
     * 将3DES加密结果的base64字符串进行解密
     *
     * @param plainText
     * @return
     * @throws Exception
     */
    public static String decryptFromBase64String(String plainText, String key) throws Exception {

        byte[] data = Base64.decodeBase64(plainText.getBytes());
        SecretKey md5Key = new SecretKeySpec(keyMD5EncodeNew(key), ALGORITHM);
        Cipher c1 = Cipher.getInstance(CIPHER_TRANSFORMAT);
        c1.init(Cipher.DECRYPT_MODE, md5Key);
        byte[] result = c1.doFinal(data);
        return new String(result, ENCODING);
    }

    /**
     * 密钥先进行MD5处理，因为java中3DES必须要求key为24位，所以要补齐24位
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] keyMD5Encode(String key) throws Exception {
        // 设置密钥
        byte[] btInput = key.getBytes(ENCODING);
        // 获得MD5摘要算法的 MessageDigest 对象
        MessageDigest mdInst = MessageDigest.getInstance("MD5");
        // 使用指定的字节更新摘要
        mdInst.update(btInput);
        // 获得MD5密文
        byte[] md = mdInst.digest();

        //16位秘钥可以按 前8位+后8位+前8位 的规则来升级成24位的秘钥
        byte[] outputKey = new byte[24];
        try {
            System.arraycopy(md, 0, outputKey, 0, 16);
            System.arraycopy(md, 0, outputKey, 16, 8);
        } catch (Exception e) {
            throw new RuntimeException("16秘钥转24秘钥异常");
        }
        return outputKey;
    }

    /**
     * 密钥先进行MD5处理，因为java中3DES必须要求key为24位，所以要补齐24位
     *
     * @param key
     * @return
     * @throws Exception
     */
    public static byte[] keyMD5EncodeNew(String key) throws Exception {
        // 设置密钥
        byte[] btInput = key.getBytes(ENCODING);
        // 获得MD5摘要算法的 MessageDigest 对象
        MessageDigest mdInst = MessageDigest.getInstance("MD5");
        // 使用指定的字节更新摘要
        mdInst.update(btInput);
        // 获得MD5密文
        byte[] md = mdInst.digest();

        //16位秘钥可以按 前8位+后8位+前8位 的规则来升级成24位的秘钥
        byte[] outputKey = new byte[24];
        try {
            System.arraycopy(md, 0, outputKey, 0, 16);
            //System.arraycopy(md, 0, outputKey, 16, 8);
        } catch (Exception e) {
            throw new RuntimeException("16秘钥转24秘钥异常");
        }
        return outputKey;
    }

    private static String bytesToHexFun2(byte[] bytes) {
        char[] buf = new char[bytes.length * 2];
        int index = 0;
        // 利用位运算进行转换，可以看作方法一的变种
        for (byte b : bytes) {
            buf[index++] = HEX_CHAR[b >>> 4 & 0xf];
            buf[index++] = HEX_CHAR[b & 0xf];
        }
        return new String(buf);
    }
}