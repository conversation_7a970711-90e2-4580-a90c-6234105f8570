package com.gsms.internet.gateway.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class AesUtils {

    private static final String ALGORITHM = "AES";

    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";

    /*public static void main(String [] args) throws Exception {
        System.out.println(AesUtils.encrypt("ssss","12346hfnsjhdjfki"));
    }*/

    /**
     * AES加密字符串
     *
     * @param message 待加密的字符串
     * @param key     密钥
     * @return base64 密文
     * @throws Exception
     */
    public static String encrypt(String message, String key) throws Exception {
        if (message == null) {
            return null;
        }
        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        // 创建密码器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        byte[] byteContent = message.getBytes(StandardCharsets.UTF_8);
        // 初始化为加密模式的密码器
        cipher.init(Cipher.ENCRYPT_MODE, secretKey);
        // 加密后的byte数据
        byte[] bytes = cipher.doFinal(byteContent);
        // 转base64
        return Base64.getEncoder().encodeToString(bytes);
    }

    /**
     * 解密AES加密过的字符串
     *
     * @param encryptMessage 带解密的字符串
     * @param key            密钥
     * @return 解密后的字符串
     * @throws Exception
     */
    public static String decrypt(String encryptMessage, String key) throws Exception {
        if (encryptMessage == null) {
            return null;
        }

        byte[] decodeArray = Base64.getDecoder().decode(encryptMessage);
        // AES专用密钥
        SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(), ALGORITHM);
        // 创建密码器
        Cipher cipher = Cipher.getInstance(TRANSFORMATION);
        // 初始化为解密模式的密码器
        cipher.init(Cipher.DECRYPT_MODE, secretKey);
        // 明文数组
        byte[] bytes = cipher.doFinal(decodeArray);
        return new String(bytes, StandardCharsets.UTF_8);
    }
}