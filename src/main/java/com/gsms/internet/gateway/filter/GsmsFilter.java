package com.gsms.internet.gateway.filter;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.gsms.internet.gateway.bean.ClientCode;
import com.gsms.internet.gateway.bean.HKYZHttpBody;
import com.gsms.internet.gateway.bean.UserInfoBo;
import com.gsms.internet.gateway.config.ClientCodeConfig;
import com.gsms.internet.gateway.dto.ResponseDTO;
import com.gsms.internet.gateway.enums.ClientCodeEnum;
import com.gsms.internet.gateway.enums.ErrorCodeEnum;
import com.gsms.internet.gateway.exception.BizException;
import com.gsms.internet.gateway.exception.TokenExpiredException;
import com.gsms.internet.gateway.log.LogAdapter;
import com.gsms.internet.gateway.properties.ErrorCodeProperties;
import com.gsms.internet.gateway.properties.IgnoreWhiteProperties;
import com.gsms.internet.gateway.service.HttpBodyDecrypter;
import com.gsms.internet.gateway.service.TokenValidator;
import com.gsms.internet.gateway.service.impl.DefaultHttpBodyDecrypter;
import com.gsms.internet.gateway.service.impl.HarmonyBangBangHttpBodyDecrypter;
import com.gsms.internet.gateway.service.impl.HarmonyHttpBodyDecrypter;
import com.gsms.internet.gateway.service.impl.TokenValidatorImpl;
import com.gsms.internet.gateway.util.JSONUtils;
import com.gsms.internet.gateway.util.MD5Utils;
import com.gsms.internet.gateway.util.RSAUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.reactivestreams.Publisher;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.cloud.gateway.filter.factory.rewrite.CachedBodyOutputMessage;
import org.springframework.cloud.gateway.support.BodyInserterContext;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ReactiveHttpOutputMessage;
import org.springframework.http.codec.HttpMessageReader;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpRequestDecorator;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.http.server.reactive.ServerHttpResponseDecorator;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.BodyInserter;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * http请求过滤器
 *
 * <AUTHOR>
 * @date 2023/10/18 09:47
 * @company 昆仑数智科技有限责任公司
 */
@Component
@RefreshScope
@Slf4j
public class GsmsFilter implements GlobalFilter, GatewayFilter, Ordered {

    private final List<HttpMessageReader<?>> messageReaders = HandlerStrategies.withDefaults().messageReaders();

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private IgnoreWhiteProperties ignoreWhiteProperties;

    @Value("${spring.profiles.active}")
    private String env;

    @Autowired
    private ClientCodeConfig clientCodeConfig;

    @Autowired
    private ErrorCodeProperties codeProperties;

    /**
     * http请求过滤器
     *
     * <AUTHOR>
     * @date 2023/10/18 09:47
     * @company 昆仑数智科技有限责任公司
     */
    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {

        LogAdapter.clear();
        ServerHttpRequest serverHttpRequest = exchange.getRequest();
        URI uri = serverHttpRequest.getURI();

        // route路由只作纯转发
        if (uri.getPath().startsWith("/route/")) {
            log.info("转发请求:{}", uri.getPath());
            return chain.filter(exchange);
        }

        String method = serverHttpRequest.getMethodValue();
        // 对请求做基本合规性校验
        if (!"POST".equals(method)
                || exchange.getRequest().getHeaders().getContentType() == null
                || !exchange.getRequest().getHeaders().getContentType().toString().contains(MediaType.APPLICATION_JSON_VALUE)) {
            return fail(exchange, ErrorCodeEnum.REQUEST_ERROR);
        }

        try {
            long now = System.currentTimeMillis();
            HttpHeaders httpHeaders = serverHttpRequest.getHeaders();
            String methodName = uri.getPath();
            String contentType = serverHttpRequest.getHeaders().getFirst(HttpHeaders.CONTENT_TYPE);

            // 1. 校验header
            String traceId = httpHeaders.getFirst("trace-id");
            String token = httpHeaders.getFirst("token");
            String clientCode = httpHeaders.getFirst("client-code");
            String time = httpHeaders.getFirst("time");
            String sign = httpHeaders.getFirst("sign");
            String riskField = httpHeaders.getFirst("risk-field");
            // 欠缺必要header字段时直接报错
            if (StringUtils.isBlank(clientCode) || StringUtils.isBlank(traceId)
                    || StringUtils.isBlank(time)) {
                return fail(exchange, ErrorCodeEnum.PARAM_ERROR);
            }
            MDC.put("UUID", traceId);
            MDC.put("ori", clientCode);
            LogAdapter.setLog(traceId, clientCode);

            // 2.校验来源渠道
            ClientCode resultEntity = clientCodeConfig.getConfigs().getOrDefault(clientCode, null);
            if (resultEntity == null) {
                return fail(exchange, ErrorCodeEnum.CLIENT_CODE_ERROR);
            }

            // 3.获取网关渠道对应的业务渠道
            String sourceCode = "";
            ClientCodeEnum clientCodeEnum = ClientCodeEnum.getByClientCode(clientCode);

            TokenValidator tokenValidator = getTokenValidator(clientCodeEnum); // 前端用户token校验器
            // 获取3.0中台需要的前端的业务渠道号
            if (clientCodeEnum != null) {
                sourceCode = resultEntity.getSourceCode();
            }

            // 4.校验时间戳
            if (!isValidOfTimeStamp(time)) {
                return fail(exchange, ErrorCodeEnum.TIME_ERROR);
            }

            // 5.获取body
            String body = getBody(exchange);
            if (StringUtils.isBlank(body)) {
                return fail(exchange, ErrorCodeEnum.REQUEST_ERROR);
            }
            log.error("请求url:{},原始参数-header:{},原始参数-body:{}", methodName, httpHeaders, body);

            // 5.1 对body进行解密
            String signBody;
            HttpBodyDecrypter httpBodyDecrypter = getHttpBodyDecrypter(httpHeaders, clientCode);
            try {
                HKYZHttpBody hkyzHttpBody = httpBodyDecrypter.decrypt(body, clientCode, methodName);
                signBody = hkyzHttpBody.getSignBody();
                body = hkyzHttpBody.getBody();
            } catch (BizException bizException) {
                log.error(bizException.getMessage());
                return fail(exchange, ErrorCodeEnum.HTTP_BODY_ERROR);
            } catch (Exception e) {
                log.error("请求体解密失败", e);
                return fail(exchange, ErrorCodeEnum.HTTP_BODY_ERROR);
            }


            // 6.校验签名
            if (!validSign(time, sign, resultEntity, signBody)) {
                return fail(exchange, ErrorCodeEnum.SIGN_ERROR);
            }

            // 7.校验token
            String userId = "";
            String authorization = "";
            try {
                // 判断token白名单
                if (!include(ignoreWhiteProperties.getUris(), methodName, token)) {
                    UserInfoBo userInfoBo = tokenValidator.validTokenAndGetUserInfo(token, clientCode);
                    userId = userInfoBo.getUserId();
                    authorization = userInfoBo.getAuthDTO().getAuthorization();
                }
            } catch (TokenExpiredException te) {
                return fail(exchange, ErrorCodeEnum.TOKEN_EXPIRED);
            } catch (Exception e) {
                return fail(exchange, ErrorCodeEnum.AUTH_ERROR);
            }
            MDC.put("userId", userId);
            LogAdapter.setLog(userId, traceId, clientCode);

            // 8.构造新的header
            HttpHeaders newHeader = buildNewHeader(serverHttpRequest, authorization, traceId,
                    contentType, userId, sourceCode, clientCode, riskField);

            // 9.针对body做一些定制处理
            body = handleBody(body, serverHttpRequest, sourceCode);

            // 10.组装最终http参数
            final String fBody = body;
            final String fUserId = userId;
            final String fTraceId = traceId;
            ServerRequest serverRequest = ServerRequest.create(exchange, messageReaders);
            Mono<byte[]> modifiedBody = serverRequest.bodyToMono(byte[].class).flatMap(s -> {
                byte[] bytes = fBody.getBytes(StandardCharsets.UTF_8);
                return Mono.just(bytes);
            });
            BodyInserter<Mono<byte[]>, ReactiveHttpOutputMessage> bodyInserter = BodyInserters.fromPublisher(modifiedBody, byte[].class);
            newHeader.setContentLength(fBody.getBytes().length);
            CachedBodyOutputMessage outputMessage = new CachedBodyOutputMessage(exchange, newHeader);

            return bodyInserter.insert(outputMessage, new BodyInserterContext())
                    .then(Mono.defer(() -> {
                        // 重新封装请求
                        ServerHttpRequest decoratedRequest = requestDecorate(exchange, newHeader, outputMessage);
                        // 记录响应日志
                        ServerHttpResponseDecorator decoratedResponse = recordResponseLog(exchange, fUserId, fTraceId, clientCode, now);
                        printRequestLog(newHeader, fBody, methodName);
                        return chain.filter(exchange.mutate().request(decoratedRequest).response(decoratedResponse).build()).doFinally(e -> LogAdapter.clear());
                    }));
        } catch (Exception e) {
            log.error("gatewayError:网关错误", e);
            return fail(exchange, ErrorCodeEnum.FAILED);
        }
    }

    private ServerHttpRequestDecorator requestDecorate(ServerWebExchange exchange, HttpHeaders headers,
                                                       CachedBodyOutputMessage outputMessage) {
        return new ServerHttpRequestDecorator(exchange.getRequest()) {
            @Override
            public HttpHeaders getHeaders() {
                return headers;
            }

            @Override
            public Flux<DataBuffer> getBody() {
                return outputMessage.getBody();
            }
        };
    }

    private String handleBody(String body, ServerHttpRequest serverHttpRequest, String sourceCode) {
        if (StringUtils.isBlank(body) || "{}".equals(body) || "[]".equals(body)) {
            return body;
        }
        JsonNode jsonNode = JSONUtils.parse(body);
        JsonNode extendFiledNode = jsonNode.get("extendFiled");
        if (extendFiledNode == null) {
            return body;
        }
        ObjectNode objectNode = (ObjectNode) extendFiledNode;
        objectNode.put("operIp", getIpAddress(serverHttpRequest));
        if (StringUtils.isNotBlank(sourceCode)) {
            objectNode.put("sourceChan", sourceCode);
        }
        return jsonNode.toString();
    }

    private HttpHeaders buildNewHeader(ServerHttpRequest serverHttpRequest, String authorization,
                                       String traceId, String contentType, String userId, String sourceCode,
                                       String clientCode, String riskField) {
        HttpHeaders result = new HttpHeaders();
        HttpHeaders httpHeaders = serverHttpRequest.getHeaders();
        result.put("authorization", Collections.singletonList(authorization));
        result.put("userId", Collections.singletonList(userId));
        result.put("ip", Collections.singletonList(getIpAddress(serverHttpRequest)));
        String deviceName = httpHeaders.getFirst("device-name");
        result.put("deviceName", Collections.singletonList(deviceName));
        result.put("deviceId", Collections.singletonList(httpHeaders.getFirst("device-id")));
        result.put("deviceNo", Collections.singletonList(httpHeaders.getFirst("device-no")));
        result.put("UUID", Collections.singletonList(traceId));
        result.put("time", Collections.singletonList(httpHeaders.getFirst("time")));
        result.put("x-api-version", Collections.singletonList(httpHeaders.getFirst("x-api-version")));
        if (StringUtils.isNotBlank(sourceCode)) {
            result.put("sourceCode", Collections.singletonList(sourceCode));
        } else {
            result.put("sourceCode", Collections.singletonList(""));
        }
        result.put("clientCode", Collections.singletonList(clientCode));
        if (StringUtils.isNotBlank(riskField)) {
            result.put("riskField", Collections.singletonList(riskField));
        } else {
            result.put("riskField", Collections.singletonList(""));
        }
        // 添加app的版本号
        String hkVersion = httpHeaders.getFirst("hkversion");
        if (StringUtils.isEmpty(hkVersion)) {
            hkVersion = httpHeaders.getFirst("hkVersion");
        }
        // 非app的版本号，获取小程序的版本号
        if (StringUtils.isEmpty(hkVersion) && !ClientCodeEnum.APP.getClientCode().equals(clientCode)) {
            hkVersion = httpHeaders.getFirst("miniappVersion");
        }
        if (StringUtils.isNotBlank(hkVersion)) {
            result.put("hkVersion", Collections.singletonList(hkVersion));
        }
        result.put("ori", Collections.singletonList(clientCode));
        if (StringUtils.isNotEmpty(contentType)) {
            result.set(HttpHeaders.CONTENT_TYPE, contentType);
        }
        return result;
    }

    private boolean validSign(String time, String sign, ClientCode resultEntity, String signBody) {
        try {
            if (StringUtils.isBlank(sign)) {
                return false;
            }
            Integer type = resultEntity.getType();
            if (type != null) {
                String newSign = "";
                if (type == 1) {
                    // MD5 + key + time
                    newSign = MD5Utils.MD5(signBody + resultEntity.getKeyMd5() + time);
                    log.info("MD5加密后的签名:{} , 签名参数{}", newSign, signBody + resultEntity.getKeyMd5() + time);
                    return !StringUtils.isBlank(newSign) && newSign.equals(sign);
                } else if (type == 2) {
                    return RSAUtils.verify(signBody, sign, RSAUtils.getPublicKey(resultEntity.getPublicKey()));
                }
            }
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private Mono<Void> fail(ServerWebExchange exchange, ErrorCodeEnum errorCodeEnum, String... data) {
        ResponseDTO<String> responseDTO = new ResponseDTO<>();
        responseDTO.setSuccess(false);
        responseDTO.setErrorCode(errorCodeEnum.getCode());
        responseDTO.setMessage(errorCodeEnum.getDesc());
        log.error("gatewayError:" + errorCodeEnum.getCode());
        if (data != null && data.length > 0) {
            responseDTO.setData(data[0]);
        } else {
            responseDTO.setData("");
        }
        byte[] bits = JSONUtils.toJSONString(responseDTO).getBytes(StandardCharsets.UTF_8);
        ServerHttpResponse serverHttpresponse = exchange.getResponse();
        DataBuffer buffer = serverHttpresponse.bufferFactory().wrap(bits);
        serverHttpresponse.setStatusCode(HttpStatus.OK);
        serverHttpresponse.getHeaders().add("Content-Type", "application/json");
        return serverHttpresponse.writeWith(Mono.just(buffer)).doFinally(e -> LogAdapter.clear());
    }

    public String getIpAddress(ServerHttpRequest request) {
        HttpHeaders headers = request.getHeaders();
        String ip = headers.getFirst("x-forwarded-for");
        if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
            // 多次反向代理后会有多个ip值，第一个ip才是真实ip
            if (ip.contains(",")) {
                ip = ip.split(",")[0];
            }
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("WL-Proxy-Client-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_CLIENT_IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("HTTP_X_FORWARDED_FOR");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = headers.getFirst("X-Real-IP");
        }
        if (StringUtils.isBlank(ip) || "unknown".equalsIgnoreCase(ip)) {
            ip = Objects.requireNonNull(request.getRemoteAddress()).getAddress().getHostAddress();
        }
        return ip;
    }

    private boolean isValidOfTimeStamp(String strTimeStamp) {
        if ("dev".equals(env)) {
            return true;
        }
        long oldTs = Long.parseLong(strTimeStamp);
        long currTs = System.currentTimeMillis();
        return 5 * 60 * 1000 >= (currTs - oldTs);
    }

    private boolean include(List<IgnoreWhiteProperties.AuthUri> uris, String methodName, String token) {
        if (CollectionUtils.isEmpty(uris)) {
            return false; // 校验token
        }
        if (methodName.contains("?")) {
            methodName = methodName.substring(0, methodName.indexOf("?"));
        }
        for (IgnoreWhiteProperties.AuthUri authUri : uris) {
            String uri = authUri.getUri();
            String authType = authUri.getAuthType();
            if (methodName.equals(uri) && authType.equals("1")) {
                return true; // 不校验token
            } else if (methodName.equals(uri) && authType.equals("2") && StringUtils.isBlank(token)) {
                return true; // 不校验token
            } else if (methodName.equals(uri) && authType.equals("2") && StringUtils.isNotBlank(token)) {
                return false; // 校验token
            }
        }
        return false;
    }

    private String getBody(ServerWebExchange serverWebExchange) {
        DataBuffer body = serverWebExchange.getAttribute("cachedRequestBody");
        if (body == null) {
            log.warn("请求体为空");
            return "";
        }
        return body.toString(StandardCharsets.UTF_8);
    }

    private void printRequestLog(HttpHeaders httpHeaders, String body, String uri) {
        Map<String, String> headers = new HashMap<>();
        httpHeaders.forEach((name, value) -> {
            headers.put(name, value.get(0));
        });
        log.info("请求url:{},最终请求header:{},最终请求body:{}", uri, JSONUtils.toJSONStringIgnoreError(headers), body);
    }

    @Override
    public int getOrder() {
        return -100;
    }

    private ServerHttpResponseDecorator recordResponseLog(ServerWebExchange exchange, String userId, String traceId, String ori, long start) {
        ServerHttpResponse response = exchange.getResponse();
        DataBufferFactory bufferFactory = response.bufferFactory();
        return new ServerHttpResponseDecorator(response) {
            @Override
            @NonNull
            public Mono<Void> writeWith(@NonNull Publisher<? extends DataBuffer> body) {
                if (body instanceof Flux) {
                    if ((Objects.equals(this.getStatusCode(), HttpStatus.OK)
                            || Objects.equals(this.getStatusCode(), HttpStatus.INTERNAL_SERVER_ERROR)) &&
                            response.getHeaders().getContentType() != null &&
                            response.getHeaders().getContentType().toString().contains(MediaType.APPLICATION_JSON_VALUE)) {
                        Flux<? extends DataBuffer> fluxBody = Flux.from(body);
                        return super.writeWith(fluxBody.buffer().map(dataBuffers -> {
                            MDC.put("UUID", traceId);
                            MDC.put("userId", userId);
                            MDC.put("ori", ori);
                            LogAdapter.setLog(userId, traceId, ori);

                            // 获取response-body
                            String result = "";
                            DataBufferFactory dataBufferFactory = getDelegate().bufferFactory();
                            DataBuffer join = dataBufferFactory.join(dataBuffers);
                            byte[] content = new byte[join.readableByteCount()];
                            join.read(content);
                            DataBufferUtils.release(join);
                            result = new String(content, StandardCharsets.UTF_8);


                            // 转换错误码提示语
                            JsonNode jsonNode = JSONUtils.parse(result);
                            JsonNode eNode = jsonNode.get("errorCode");
                            if (eNode != null && StringUtils.isNotBlank(eNode.asText())
                                    && !"null".equalsIgnoreCase(eNode.asText())
                                    && codeProperties.getMsg().containsKey(eNode.asText())) {
                                String newMessage = codeProperties.getMsg().get(eNode.asText());
                                result = JSONUtils.replaceAndGenerateNewString(jsonNode, "message", newMessage);
                            }

                            log.info("响应耗时[{}]ms,url:{},响应body:{}", System.currentTimeMillis() - start,
                                    exchange.getRequest().getURI().getPath(), result);

                            // 重新设置content-length
                            byte[] newRs = result.getBytes(StandardCharsets.UTF_8);
                            this.getDelegate().getHeaders().setContentLength(newRs.length);
                            return bufferFactory.wrap(newRs);
                        }).doOnDiscard(DataBuffer.class, DataBufferUtils::release)).doFinally(e -> LogAdapter.clear());
                    }
                }
                return super.writeWith(body);
            }
        };
    }

    private TokenValidator getTokenValidator(ClientCodeEnum clientCodeEnum) {
        return applicationContext.getBean(TokenValidatorImpl.class);
    }

    private HttpBodyDecrypter getHttpBodyDecrypter(HttpHeaders httpHeaders, String clientCode) {
        String platform = httpHeaders.getFirst("platform");
        if (StringUtils.isNotBlank(platform) && platform.equalsIgnoreCase("harmony")
                && clientCode.equals(ClientCodeEnum.APP.getClientCode())) {
            return applicationContext.getBean(HarmonyBangBangHttpBodyDecrypter.class);
        } else if (clientCode.equals(ClientCodeEnum.YUANFUWU.getClientCode())) {
            return applicationContext.getBean(HarmonyHttpBodyDecrypter.class);
        } else {
            return applicationContext.getBean(DefaultHttpBodyDecrypter.class);
        }
    }

}
