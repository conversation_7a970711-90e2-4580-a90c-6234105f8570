package com.gsms.internet.gateway.service;


import com.gsms.internet.gateway.bean.UserInfoBo;
import com.gsms.internet.gateway.exception.TokenExpiredException;

public interface TokenValidator {
    /**
     * 校验token，并获取用户信息
     *
     * <AUTHOR>
     * @date 2022/10/17 16:25
     * @company 昆仑数智科技有限责任公司
     */
    UserInfoBo validTokenAndGetUserInfo(String token, String clientCode) throws TokenExpiredException;
}
