--Copy right sunboxsoft
--WAF config file,enable = "on",disable = "off"

--waf status
config_waf_enable = "on"
--log dir
config_log_dir = "/Users/<USER>/Desktop/waf/logs"
--config_log_dir = "/data/logs/waflogs"
--rule setting
config_rule_dir = "/Users/<USER>/Desktop/waf/rule-config"
--config_rule_dir = "/home/<USER>/waf/rule-config"
--config_rule_dir = "/home/<USER>/waf/rule-config"
--enable/disable white url
config_white_url_check = "on"
--enable/disable white ip
config_white_ip_check = "on"
config_black_ip_check_log="on"
--enable/disable block ip
config_black_ip_check = "on"
--enable/disable url filtering
config_url_check = "on"
--enalbe/disable url args filtering
config_url_args_check = "on"
--enable/disable user agent filtering
config_user_agent_check = "on"
--enable/disable cookie deny filtering
config_cookie_check = "on"
--enable/disable cc filtering
config_cc_check = "on"
--cc rate the xxx of xxx seconds
config_cc_rate = "60/60"
--all path blackchek
config_allpath_check ="on"
--enable/disable post filtering
config_post_check = "on"
--config waf output redirect/html
config_waf_output = "html"
--if config_waf_output ,setting url
config_waf_redirect_url = "https://www.baidu.com"
config_output_html=[[
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="Content-Language" content="zh-cn" />
<title></title>
</head>
<body>
<h1 align="center">您的访问过于频繁，请稍后再试</h1>
</body>
</html>
]]

config_autofilterip_check="on"
config_tokencheck_check="off"  --广东版本支持，其他请改为off
config_tokencheck_turnhtml=""
config_autofilterip_bind_time = 120  
config_autofilterip_time_out = 60
config_autofilterip_connect_count = 8 


config_redis_ip=""
config_redis_port=6379
config_redis_pass=""
config_redis_database=0
config_redis_timeout=10000

--kafka 日志归集配置begin
config_logkafka_enable = "off"
--kafka 地址
config_kafka_ip=""
--kafka 端口
config_kafka_port=""
--kafka 日志归集配置end


