package com.gsms.internet.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.reactive.ReactorResourceFactory;

@Configuration
@Slf4j
public class ReactorConfig {

    @Bean
    public ReactorResourceFactory reactorClientResourceFactory() {
        System.setProperty("reactor.netty.ioSelectCount", "1");
        int ioWorkerCount = (Runtime.getRuntime().availableProcessors()) * 3;
        System.setProperty("reactor.netty.ioWorkerCount", String.valueOf(ioWorkerCount));
        log.info("reactor.netty.ioWorkerCount:{}", ioWorkerCount);
        return new ReactorResourceFactory();
    }
}
