package com.gsms.internet.gateway.util;

import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.api.geo.GeoSearchArgs;
import org.redisson.client.codec.StringCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * redis通用工具类
 *
 * <AUTHOR>
 * @date 2022/7/8 09:49
 * @company 昆仑数智科技有限责任公司
 */
@Slf4j
@Component
public class RedisUtils {

    private RedissonClient redissonClient;

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    public boolean expire(String key, long time) {
        if (time <= 0) {
            return false;
        }
        RBucket<String> rBucket = redissonClient.getBucket(key);
        rBucket.expire(time, TimeUnit.SECONDS);
        return true;
    }

    public boolean expire(String key, long time, TimeUnit timeUnit) {
        if (time <= 0) {
            return false;
        }
        RBucket<String> rBucket = redissonClient.getBucket(key);
        rBucket.expire(time, timeUnit);
        return true;
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */

    public boolean hasKey(String key) {
        RBucket<String> rBucket = redissonClient.getBucket(key);
        return rBucket.isExists();
    }

    /**
     * 删除 key
     *
     * @param key 键
     */
    public void delete(String... key) {
        if (key == null || key.length < 1) {
            return;
        }
        for (String k : key) {
            RBucket<String> rBucket = redissonClient.getBucket(k);
            if (rBucket.isExists()) {
                rBucket.delete();
                log.info("从缓存中删除数据,key={}", k);
            }
        }
    }

    public String get(String key) {
        if (!StringUtils.hasLength(key)) {
            return null;
        }
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        String result = rBucket.get();
        if (!StringUtils.hasLength(result)) {
            return null;
        }
        long l1 = System.currentTimeMillis();
        log.info("从缓存中获取数据,key=【{}】,耗时=【{}】毫秒", key, l1 - l);
        return result;

    }

    public <T> T get(String key, Class<T> clazz) {
        if (!StringUtils.hasLength(key)) {
            return null;
        }
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        String str = rBucket.get();
        if (!StringUtils.hasLength(str)) {
            return null;
        }
        T object = JSONUtils.parseObject(str, clazz);
        long l1 = System.currentTimeMillis();
        log.info("从缓存中获取数据,key=【{}】,耗时=【{}】毫秒", key, l1 - l);
        return object;

    }

    public void set(String key, String value) {
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        rBucket.set(value);
        long l1 = System.currentTimeMillis();
        log.info("向缓存中设置数据,key=【{}】,耗时=【{}】毫秒", key, l1 - l);

    }

    public <T> boolean set(String key, T value) {
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        String str = JSONUtils.toJSONString(value);
        rBucket.set(str);
        long l1 = System.currentTimeMillis();
        log.info("向缓存中设置数据,key=【{}】,耗时=【{}】毫秒", key, l1 - l);
        return true;

    }

    public <T> void set(String key, T value, long time, TimeUnit timeUnit) {
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        String str = JSONUtils.toJSONString(value);
        rBucket.set(str, time, timeUnit);
        long l1 = System.currentTimeMillis();
        log.info("向缓存中设置数据,key=【{}】,超时时间=【{}】,耗时=【{}】毫秒", key, time, l1 - l);
    }

    public void set(String key, String value, long time, TimeUnit timeUnit) {
        long l = System.currentTimeMillis();
        RBucket<String> rBucket = redissonClient.getBucket(key);
        rBucket.set(value, time, timeUnit);
        long l1 = System.currentTimeMillis();
        log.info("向缓存中设置数据,key=【{}】,超时时间=【{}】,耗时=【{}】毫秒", key, time, l1 - l);

    }

    public long incr(String key) {
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key);
        return rAtomicLong.incrementAndGet();
    }

    public long getIncrValue(String key) {
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(key);
        return rAtomicLong.get();
    }

    /**
     * 获取分布式锁,如果没获取到则返回null
     * 如果获取到，会自动进行锁续期
     * 必须在final块里释放锁！！！
     *
     * <AUTHOR>
     * @date 2022/10/25 17:11
     * @company 昆仑数智科技有限责任公司
     */
    public RLock tryLock(String key) {
        RLock redissonLock = redissonClient.getLock(key);
        if (redissonLock.tryLock()) {
            log.info("成功获取到分布式锁【{}】", key);
            return redissonLock;
        }
        log.info("获取分布式锁【{}】失败", key);
        return null;
    }

    public RLock tryLock(String key, long time, TimeUnit timeUnit) {
        RLock redissonLock = redissonClient.getLock(key);
        try {
            if (redissonLock.tryLock(time, timeUnit)) {
                log.info("成功获取到分布式锁【{}】", key);
                return redissonLock;
            }
        } catch (InterruptedException e) {
            log.error(e.getMessage(), e);
        }
        log.info("获取分布式锁【{}】失败", key);
        return null;
    }

    /**
     * 释放分布式锁
     *
     * <AUTHOR>
     * @date 2022/10/25 17:19
     * @company 昆仑数智科技有限责任公司
     */
    public void unLock(RLock redissonLock) {
        if (redissonLock != null
                && redissonLock.isLocked()
                && redissonLock.isHeldByCurrentThread()) {
            redissonLock.unlock();
            log.info("释放分布式锁成功");
        }
    }

    public void hSet(String key, String hashKey, String value) {
        long l1 = System.currentTimeMillis();
        RMap<String, String> map = redissonClient.getMap(key);
        map.put(hashKey, value);
        long l2 = System.currentTimeMillis();
        log.info("向缓存中设置数据,key=【{}】, hashKey =【{}】, 耗时=【{}】毫秒", key, hashKey, l2 - l1);
    }

    public void hSet(String key, String hashKey, String value, long time, TimeUnit timeUnit) {
        long l1 = System.currentTimeMillis();
        boolean b = false;
        RMap<String, String> map = redissonClient.getMap(key);
        if (!map.isExists()) {
            b = true;
        }
        map.put(hashKey, value);
        long l2 = System.currentTimeMillis();
        if (b) {
            map.expire(time, timeUnit);
        }
        log.info("向缓存中设置数据,key=【{}】, hashKey =【{}】, 耗时=【{}】毫秒", key, hashKey, l2 - l1);
    }

    public String hGet(String key, String hashKey) {
        long l1 = System.currentTimeMillis();
        RMap<String, String> map = redissonClient.getMap(key);
        if (!map.isExists()) {
            return null;
        }
        String value = map.get(key);
        long l2 = System.currentTimeMillis();
        log.info("从缓存中获取数据,key=【{}】, hashKey =【{}】, 耗时=【{}】毫秒", key, hashKey, l2 - l1);
        return value;
    }

    /**
     * 增加坐标
     *
     * <AUTHOR>
     * @date 2025/5/27 10:30
     * @company 昆仑数智科技有限责任公司
     */
    public void geoAdd(String key, String name, double longitude, double latitude) {
        long l1 = System.currentTimeMillis();
        RGeo<String> geo = redissonClient.getGeo(key);
        geo.add(new GeoEntry(longitude, latitude, name));
        long l2 = System.currentTimeMillis();
        log.info("向缓存中设置经纬度数据,key=【{}】, name =【{}】, 耗时=【{}】毫秒", key, name, l2 - l1);
    }

    /**
     * 获取两个坐标之间的直线距离
     *
     * <AUTHOR>
     * @date 2025/5/27 10:30
     * @company 昆仑数智科技有限责任公司
     */
    public double geoDistance(String key, String name1, String name2) {
        long l1 = System.currentTimeMillis();
        RGeo<String> geo = redissonClient.getGeo(key);
        if (!geo.isExists()) {
            throw new RuntimeException("key=" + key + " 不存在");
        }
        Double distance = geo.dist(name1, name2, GeoUnit.METERS);
        long l2 = System.currentTimeMillis();
        log.info("从缓存中计算位置距离,key=【{}】, name =【{}】, 耗时=【{}】毫秒", key, name1 + "->" + name2, l2 - l1);
        return distance;
    }

    /**
     * 删除坐标
     *
     * <AUTHOR>
     * @date 2025/5/27 10:30
     * @company 昆仑数智科技有限责任公司
     */
    public void geoRemove(String key, String name) {
        long l1 = System.currentTimeMillis();
        RGeo<String> geo = redissonClient.getGeo(key);
        if (!geo.isExists()) {
            throw new RuntimeException("key=" + key + " 不存在");
        }
        geo.remove(name);
        long l2 = System.currentTimeMillis();
        log.info("从缓存中删除坐标,key=【{}】, name =【{}】, 耗时=【{}】毫秒", key, name, l2 - l1);
    }

    /**
     * 查询中心点半径范围内的所有地点（返回成员名称和距离）
     * 默认升序，返回所有结果
     *
     * @param longitude 中心点经度
     * @param latitude  中心点纬度
     * @param radius    半径
     * @param geoUnit   单位
     * @return 地点名称列表, 距离
     */
    public Map<String, Double> geoRadiusSearch(String key, double longitude, double latitude, double radius, GeoUnit geoUnit) {
        long l1 = System.currentTimeMillis();
        RGeo<String> geo = redissonClient.getGeo(key);
        if (!geo.isExists()) {
            throw new RuntimeException("key=" + key + " 不存在");
        }
        Map<String, Double> advancedResults = geo.searchWithDistance(
                GeoSearchArgs.from(longitude, latitude)
                        .radius(radius, geoUnit)
                        .order(GeoOrder.ASC));
        long l2 = System.currentTimeMillis();
        log.info("从缓存中查找半径地点,key=【{}】, 耗时=【{}】毫秒", key, l2 - l1);
        return advancedResults;
    }

    /**
     * 分页查询中心点半径范围内的地点及距离
     *
     * @param key       Redis GEO key
     * @param longitude 中心点经度
     * @param latitude  中心点纬度
     * @param radius    半径
     * @param geoUnit   单位 (GeoUnit.METERS/KILOMETERS/MILES/FEET)
     * @param pageNum   页码（从1开始）
     * @param pageSize  每页大小
     * @return Map<地点名称, 距离>
     */
    public Map<String, Double> geoRadiusSearch(String key, double longitude, double latitude, double radius, GeoUnit geoUnit, int pageNum, int pageSize) {
        long startTime = System.currentTimeMillis();
        if (pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于等于1");
        }
        if (pageSize < 1) {
            throw new IllegalArgumentException("每页大小必须大于等于1");
        }
        RGeo<String> geo = redissonClient.getGeo(key);
        if (!geo.isExists()) {
            throw new RuntimeException("key=" + key + " 不存在");
        }
        // 计算分页偏移量
        int offset = (pageNum - 1) * pageSize;
        Map<String, Double> results = geo.searchWithDistance(
                GeoSearchArgs.from(longitude, latitude)
                        .radius(radius, geoUnit)
                        .order(GeoOrder.ASC)
                        .count(offset + pageSize) // 获取到当前页最后一条
        );
        // 手动分页处理
        results = results.entrySet().stream().skip(offset).limit(pageSize).collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        long endTime = System.currentTimeMillis();
        log.info("从缓存中分页查找半径地点, key=【{}】, 页码=【{}】, 每页=【{}】, 耗时=【{}】毫秒",
                key, pageNum, pageSize, endTime - startTime);

        return results;
    }

    /**
     * 设置位图中指定位的值
     *
     * @param key    键
     * @param offset 偏移量(从0开始)
     * @param value  true表示1，false表示0
     * @return 原始位的布尔值
     */
    public boolean setBit(String key, long offset, boolean value) {
        long startTime = System.currentTimeMillis();
        RBitSet bitSet = redissonClient.getBitSet(key);
        boolean result = bitSet.set(offset, value);
        log.info("设置位图数据,key=【{}】,offset=【{}】,value=【{}】,耗时=【{}】毫秒",
                key, offset, value, System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取位图中指定位的值
     *
     * @param key    键
     * @param offset 偏移量(从0开始)
     * @return 位的布尔值
     */
    public boolean getBit(String key, long offset) {
        long startTime = System.currentTimeMillis();
        RBitSet bitSet = redissonClient.getBitSet(key);
        boolean result = bitSet.get(offset);
        log.info("获取位图数据,key=【{}】,offset=【{}】,耗时=【{}】毫秒",
                key, offset, System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 统计位图中值为1的位数
     *
     * @param key 键
     * @return 值为1的位数
     */
    public long bitCount(String key) {
        long startTime = System.currentTimeMillis();
        RBitSet bitSet = redissonClient.getBitSet(key);
        long count = bitSet.cardinality();
        log.info("统计位图1的位数,key=【{}】,count=【{}】,耗时=【{}】毫秒",
                key, count, System.currentTimeMillis() - startTime);
        return count;
    }


    /**
     * 获取位图长度(位数)
     * 返回的是最高位是1的位偏移量+1
     *
     * @param key 键
     * @return 位图长度
     */
    public long bitLength(String key) {
        long startTime = System.currentTimeMillis();
        RBitSet bitSet = redissonClient.getBitSet(key);
        long length = bitSet.size();
        log.info("获取位图长度,key=【{}】,length=【{}】,耗时=【{}】毫秒",
                key, length, System.currentTimeMillis() - startTime);
        return length;
    }

    // ============================== Set 操作 ==============================

    /**
     * 向Set中添加元素
     *
     * @param key   键
     * @param value 值 可以是多个
     */
    public <T> void sAdd(String key, T value) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        set.add(JSONUtils.toJSONString(value));
        log.info("向Set中添加元素,key=【{},耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
    }

    /**
     * 从Set中移除元素
     *
     * @param key   键
     * @param value 值 可以是多个
     */
    public <T> void sRemove(String key, T value) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        set.remove(JSONUtils.toJSONString(value));
        log.info("从Set中移除元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
    }

    /**
     * 判断Set中是否包含某个元素
     *
     * @param key   键
     * @param value 值
     * @return true 包含 false 不包含
     */
    public <T> boolean sContains(String key, T value) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        String jsonValue = JSONUtils.toJSONString(value);
        boolean result = set.contains(jsonValue);
        log.info("判断Set中是否包含元素,key=【{}】,value=【{}】,结果=【{}】,耗时=【{}】毫秒",
                key, jsonValue, result, System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取Set中的所有元素
     *
     * @param key   键
     * @param clazz 元素类型
     * @return Set中的所有元素
     */
    public <T> Set<T> sMembers(String key, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        Set<String> members = set.readAll();
        Set<T> result = members.stream()
                .map(json -> JSONUtils.parseObject(json, clazz))
                .collect(Collectors.toSet());
        log.info("获取Set中所有元素,key=【{}】,元素数量=【{}】,耗时=【{}】毫秒",
                key, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    public Set<String> sMembers(String key) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        Set<String> members = set.readAll();
        log.info("获取Set中所有元素,key=【{}】,元素数量=【{}】,耗时=【{}】毫秒",
                key, members.size(), System.currentTimeMillis() - startTime);
        return members;
    }

    /**
     * 获取Set的大小
     *
     * @param key 键
     * @return Set的大小
     */
    public long sSize(String key) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        long size = set.size();
        log.info("获取Set大小,key=【{}】,size=【{}】,耗时=【{}】毫秒",
                key, size, System.currentTimeMillis() - startTime);
        return size;
    }

    /**
     * 随机获取Set中的一个元素
     *
     * @param key   键
     * @param clazz 元素类型
     * @return 随机元素
     */
    public <T> T sRandomMember(String key, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        String member = set.random();
        T result = member != null ? JSONUtils.parseObject(member, clazz) : null;
        log.info("随机获取Set中元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return result;
    }

    public String sRandomMember(String key) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        String member = set.random();
        log.info("随机获取Set中元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return member;
    }

    /**
     * 随机获取Set中的一个元素并移除
     *
     * @param key   键
     * @param clazz 元素类型
     * @return 随机元素
     */
    public <T> T sRandomMemberAndRemove(String key, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        String member = set.removeRandom();
        T result = member != null ? JSONUtils.parseObject(member, clazz) : null;
        log.info("随机获取Set中元素并移除,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return result;
    }

    public String sRandomMemberAndRemove(String key) {
        long startTime = System.currentTimeMillis();
        RSet<String> set = redissonClient.getSet(key);
        String member = set.removeRandom();
        log.info("随机获取Set中元素并移除,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return member;
    }

    // ============================== ZSet 操作 ==============================

    /**
     * 向ZSet中添加元素
     *
     * @param key   键
     * @param value 值
     * @param score 分数
     * @return 是否添加成功
     */
    public <T> boolean zAdd(String key, T value, double score) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        String jsonValue = JSONUtils.toJSONString(value);
        boolean result = zset.add(score, jsonValue);
        log.info("向ZSet中添加元素,key=【{}】,score=【{}】,结果=【{}】,耗时=【{}】毫秒",
                key, score, result, System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 从ZSet中移除元素
     *
     * @param key    键
     * @param values 值
     */
    public <T> void zRemove(String key, T values) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        zset.remove(JSONUtils.toJSONString(values));
        log.info("从ZSet中移除元素,key=【{}】,耗时=【{}】毫秒",
                key, System.currentTimeMillis() - startTime);
    }

    /**
     * 获取ZSet中元素的分数
     *
     * @param key   键
     * @param value 值
     * @return 分数
     */
    public <T> Double zScore(String key, T value) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        String jsonValue = JSONUtils.toJSONString(value);
        Double score = zset.getScore(jsonValue);
        log.info("获取ZSet中元素分数,key=【{}】,score=【{}】,耗时=【{}】毫秒",
                key, score, System.currentTimeMillis() - startTime);
        return score;
    }

    /**
     * 获取ZSet中元素的排名(按分数从小到大)
     *
     * @param key   键
     * @param value 值
     * @return 排名(从0开始)
     */
    public <T> Integer zRank(String key, T value) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        String jsonValue = JSONUtils.toJSONString(value);
        Integer rank = zset.rank(jsonValue);
        log.info("获取ZSet中元素排名(升序),key=【{}】,rank=【{}】,耗时=【{}】毫秒",
                key, rank, System.currentTimeMillis() - startTime);
        return rank;
    }

    /**
     * 获取ZSet中元素的排名(按分数从大到小)
     *
     * @param key   键
     * @param value 值
     * @return 排名(从0开始)
     */
    public <T> Integer zReverseRank(String key, T value) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        String jsonValue = JSONUtils.toJSONString(value);
        Integer rank = zset.revRank(jsonValue);
        log.info("获取ZSet中元素排名(降序),key=【{}】,rank=【{}】,耗时=【{}】毫秒",
                key, rank, System.currentTimeMillis() - startTime);
        return rank;
    }

    /**
     * 获取ZSet中指定分数范围的元素(按分数从小到大)
     *
     * @param key   键
     * @param min   最小分数
     * @param max   最大分数
     * @param clazz 元素类型
     * @return 元素集合
     */
    public <T> List<T> zRangeByScore(String key, double min, double max, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRange(min, true, max, true);
        List<T> result = values.stream()
                .map(json -> JSONUtils.parseObject(json, clazz))
                .collect(Collectors.toList());
        log.info("获取ZSet中分数范围元素(升序),key=【{}】,min=【{}】,max=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, min, max, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    public List<String> zRangeByScore(String key, double min, double max) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRange(min, true, max, true);
        List<String> result = new ArrayList<>(values);
        log.info("获取ZSet中分数范围元素(升序),key=【{}】,min=【{}】,max=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, min, max, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取ZSet中指定分数范围的元素(按分数从大到小)
     *
     * @param key   键
     * @param min   最小分数
     * @param max   最大分数
     * @param clazz 元素类型
     * @return 元素集合
     */
    public <T> List<T> zReverseRangeByScore(String key, double min, double max, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRangeReversed(min, true, max, true);
        List<T> result = values.stream()
                .map(json -> JSONUtils.parseObject(json, clazz))
                .collect(Collectors.toList());
        log.info("获取ZSet中分数范围元素(降序),key=【{}】,min=【{}】,max=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, min, max, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    public List<String> zReverseRangeByScore(String key, double min, double max) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRangeReversed(min, true, max, true);
        List<String> result = new ArrayList<>(values);
        log.info("获取ZSet中分数范围元素(降序),key=【{}】,min=【{}】,max=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, min, max, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取ZSet中指定排名范围的元素(按分数从小到大)
     *
     * @param key   键
     * @param start 开始排名(从0开始)
     * @param end   结束排名
     * @param clazz 元素类型
     * @return 元素集合
     */
    public <T> List<T> zRange(String key, int start, int end, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRange(start, end);
        List<T> result = values.stream()
                .map(json -> JSONUtils.parseObject(json, clazz))
                .collect(Collectors.toList());
        log.info("获取ZSet中排名范围元素(升序),key=【{}】,start=【{}】,end=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, start, end, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    public List<String> zRange(String key, int start, int end) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRange(start, end);
        List<String> result = new ArrayList<>(values);
        log.info("获取ZSet中排名范围元素(升序),key=【{}】,start=【{}】,end=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, start, end, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 获取ZSet中指定排名范围的元素(按分数从大到小)
     *
     * @param key   键
     * @param start 开始排名(从0开始)
     * @param end   结束排名
     * @param clazz 元素类型
     * @return 元素集合
     */
    public <T> List<T> zReverseRange(String key, int start, int end, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRangeReversed(start, end);
        List<T> result = values.stream()
                .map(json -> JSONUtils.parseObject(json, clazz))
                .collect(Collectors.toList());
        log.info("获取ZSet中排名范围元素(降序),key=【{}】,start=【{}】,end=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, start, end, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    public List<String> zReverseRange(String key, int start, int end) {
        long startTime = System.currentTimeMillis();
        RScoredSortedSet<String> zset = redissonClient.getScoredSortedSet(key);
        Collection<String> values = zset.valueRangeReversed(start, end);
        List<String> result = new ArrayList<>(values);
        log.info("获取ZSet中排名范围元素(降序),key=【{}】,start=【{}】,end=【{}】,数量=【{}】,耗时=【{}】毫秒",
                key, start, end, result.size(), System.currentTimeMillis() - startTime);
        return result;
    }

    // ============================== List 操作 ==============================

    /**
     * 向List头部添加元素
     *
     * @param key   键
     * @param value 值
     */
    public <T> void lLeftPush(String key, T value) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String jsonValue = JSONUtils.toJSONString(value);
        list.add(0, jsonValue);
        log.info("向List头部添加元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
    }

    /**
     * 向List尾部添加元素
     *
     * @param key   键
     * @param value 值
     * @return 是否添加成功
     */
    public <T> boolean lRightPush(String key, T value) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String jsonValue = JSONUtils.toJSONString(value);
        boolean result = list.add(jsonValue);
        log.info("向List尾部添加元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return result;
    }

    /**
     * 从List头部移除并返回元素
     *
     * @param key   键
     * @param clazz 元素类型
     * @return 移除的元素
     */
    public <T> T lLeftPop(String key, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String value = list.remove(0);
        T result = value != null ? JSONUtils.parseObject(value, clazz) : null;
        log.info("从List头部移除元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return result;
    }

    public String lLeftPop(String key) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String value = list.remove(0);
        log.info("从List头部移除元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return value;
    }

    /**
     * 从List尾部移除并返回元素
     *
     * @param key   键
     * @param clazz 元素类型
     * @return 移除的元素
     */
    public <T> T lRightPop(String key, Class<T> clazz) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String value = list.remove(list.size() - 1);
        T result = value != null ? JSONUtils.parseObject(value, clazz) : null;
        log.info("从List尾部移除元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return result;
    }

    public String lRightPop(String key) {
        long startTime = System.currentTimeMillis();
        RList<String> list = redissonClient.getList(key);
        String value = list.remove(list.size() - 1);
        log.info("从List尾部移除元素,key=【{}】,耗时=【{}】毫秒", key, System.currentTimeMillis() - startTime);
        return value;
    }

    public Object execLuaScript(String script, Type returnType, List<Object> args) {
        long startTime = System.currentTimeMillis();
        RScript rScript = redissonClient.getScript(StringCodec.INSTANCE); // lua强制使用StringCodec，否则会报错
        Object result = rScript.eval(RScript.Mode.READ_WRITE, script, javaTypeToRedissonReturnType(returnType), args);
        log.info("执行lua脚本,返回值:{},耗时=【{}】毫秒", result, System.currentTimeMillis() - startTime);
        return result;
    }

    public Object execLuaScript(String script, Type returnType, List<Object> args, Object... values) {
        long startTime = System.currentTimeMillis();
        RScript rScript = redissonClient.getScript(StringCodec.INSTANCE); // lua强制使用StringCodec，否则会报错
        Object result = rScript.eval(RScript.Mode.READ_WRITE, script, javaTypeToRedissonReturnType(returnType), args, values);
        log.info("执行lua脚本,返回值:{},耗时=【{}】毫秒", result, System.currentTimeMillis() - startTime);
        return result;
    }

    private RScript.ReturnType javaTypeToRedissonReturnType(Type javaType) {
        if (javaType == Boolean.class || javaType == boolean.class) {
            return RScript.ReturnType.BOOLEAN;
        } else if (javaType == Long.class || javaType == long.class
                || javaType == Integer.class || javaType == int.class
                || javaType == Short.class || javaType == short.class
                || javaType == Byte.class || javaType == byte.class) {
            return RScript.ReturnType.INTEGER;
        } else if (javaType == String.class) {
            return RScript.ReturnType.STATUS;
        } else if (javaType == List.class) {
            return RScript.ReturnType.MULTI;
        } else if (javaType == Map.class) {
            return RScript.ReturnType.MAPVALUE;
        } else if (javaType == Object.class) {
            return RScript.ReturnType.VALUE; // 通用类型
        } else {
            throw new IllegalArgumentException("Unsupported Java type: " + javaType);
        }
    }

}
