package com.gsms.internet.gateway.enums;


import java.util.Objects;

/**
 * 渠道汇总枚举类
 */
public enum ClientCodeEnum {
    APP("C10", "ios/安卓/鸿蒙 APP"),
    WX_XCX("C12", "微信小程序"),
    ZFB_XCX("C13", "支付宝小程序"),
    YUANFUWU("C23", "鸿蒙元服务"),
    ;

    private final String clientCode;
    private final String desc;

    ClientCodeEnum(String clientCode, String desc) {
        this.clientCode = clientCode;
        this.desc = desc;

    }

    public static ClientCodeEnum getByClientCode(String code) {
        ClientCodeEnum[] allEnums = values();
        for (ClientCodeEnum allEnum : allEnums) {
            if (Objects.equals(allEnum.getClientCode(), code)) {
                return allEnum;
            }
        }
        return null;
    }

    public String getClientCode() {
        return this.clientCode;
    }

    public String getDesc() {
        return desc;
    }


}
