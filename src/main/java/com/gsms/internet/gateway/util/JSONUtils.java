package com.gsms.internet.gateway.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * JSON操作工具类
 *
 * <AUTHOR>
 * @date 2022/5/29 14:19
 * @company 昆仑数智科技有限责任公司
 */
public class JSONUtils {

    private static final ObjectMapper objectMapper;

    private static final ObjectMapper withOutNullMapper;

    static {
        objectMapper = new ObjectMapper();
        withOutNullMapper = new ObjectMapper();
        withOutNullMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 解决序列化没有属性对象报InvalidDefinitionException问题
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        withOutNullMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        withOutNullMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJSONString(Object obj, boolean... containNullFields) {
        boolean b = false;
        if (containNullFields.length > 0) {
            b = containNullFields[0];
        }
        try {
            return b ? objectMapper.writeValueAsString(obj) : withOutNullMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    public static String toJSONStringIgnoreError(Object obj, boolean... containNullFields) {
        try {
            return toJSONString(obj, containNullFields);
        } catch (Exception e) {
            return "";
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        if (json == null || json.equals("") || json.equals("null")) {
            return null;
        }
        try {
            return objectMapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T parseObject(byte[] input, Class<T> clazz) {
        try {
            return objectMapper.readValue(input, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> List<T> parseArray(String json, Class<T> clazz) {
        if (json == null || json.equals("")) {
            return null;
        }
        JavaType type = objectMapper.getTypeFactory().constructParametricType(List.class, clazz);
        try {
            return objectMapper.readValue(json, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static <K, V> Map<K, V> parseMap(String json, Class<K> kClazz, Class<V> vClazz) {
        if (json == null || json.equals("")) {
            return null;
        }
        JavaType type = objectMapper.getTypeFactory().constructParametricType(Map.class, kClazz, vClazz);
        try {
            return objectMapper.readValue(json, type);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static JsonNode parse(String json) {
        try {
            return objectMapper.readTree(json);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static String replaceAndGenerateNewString(JsonNode jsonNode, String param, String value) {
        ObjectNode objectNode = (ObjectNode) jsonNode;
        objectNode.put(param, value);
        return objectNode.toString();
    }

    public static String replaceAndGenerateNewString(JsonNode jsonNode, String param, int value) {
        ObjectNode objectNode = (ObjectNode) jsonNode;
        objectNode.put(param, value);
        return objectNode.toString();
    }

    public static String replaceAndGenerateNewString(JsonNode jsonNode, String param, long value) {
        ObjectNode objectNode = (ObjectNode) jsonNode;
        objectNode.put(param, value);
        return objectNode.toString();
    }

    /**
     * 判断是否是json对象
     */
    public static boolean isJsonObject(String json) {
        try {
            // 尝试将字符串解析为JsonNode对象
            JsonNode jsonNode = objectMapper.readTree(json);
            // 判断解析结果是否为对象类型
            return jsonNode.isObject();
        } catch (JsonProcessingException e) {
            // 解析失败，不是有效的JSON对象
            return false;
        }
    }

}
