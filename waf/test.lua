-- 引入相关依赖包
require 'init'

-- openresty访问redis实现token黑名单过滤
function accessToken()
    local token = ngx.req.get_headers()["token"];
    if token == nil then
        return true
    end
    local blockToken = "black_token_" .. token;
    local value = getcachedata(blockToken);
    -- redis返回数据为空时，返回的是ngx.null类型
    if value ~= nil and value ~= '' and value ~= ngx.null then
        return false
    end
    return true
end

function access()
    if accessToken() then

    else
        returnErrorJson("访问控制,拒绝访问");
        return ngx.exit(200);
    end
end

access()