--waf core lib
require 'config'
require 'redisfac'


--Get the client IP
function get_client_ip()
    CLIENT_IP = ngx.req.get_headers()["X_real_ip"]
    if CLIENT_IP == nil then
        CLIENT_IP = ngx.req.get_headers()["X_Forwarded_For"]
    end
    if CLIENT_IP == nil then
        CLIENT_IP = ngx.req.get_headers()["X-Forwarded-For"]
    end
    if CLIENT_IP == nil then
        CLIENT_IP  = ngx.var.remote_addr
    end
    if CLIENT_IP == nil then
        CLIENT_IP  = "unknown"
    end
    if type(CLIENT_IP)=="table" then
        return CLIENT_IP[1]
    end
    return CLIENT_IP
end

--Get the client user agent
function get_user_agent()
    USER_AGENT = ngx.var.http_user_agent
    if USER_AGENT == nil then
       USER_AGENT = "unknown"
    end
    return USER_AGENT
end

function get_request_url()
    REQUEST_URL=ngx.var.scheme .. ngx.var.server_addruri
    if REQUEST_URL == nil then
        REQUEST_URL="unknown"
    end
    return REQUEST_URL
end

--Get WAF rule
function get_rule(rulefilename)
    local io = require 'io'
    local RULE_PATH = config_rule_dir
    local RULE_FILE = io.open(RULE_PATH..'/'..rulefilename,"r")
    if RULE_FILE == nil then
        return
    end
    RULE_TABLE = {}
    for line in RULE_FILE:lines() do
        table.insert(RULE_TABLE,line)
    end
    RULE_FILE:close()
    return(RULE_TABLE)
end

--WAF log record for json,(use logstash codec => json)
function log_record(method,url,data,ruletag)
    local cjson = require("cjson")
    local io = require 'io'
    local LOG_PATH = config_log_dir
    local CLIENT_IP = get_client_ip()
    local USER_AGENT = get_user_agent()
    local SERVER_NAME = ngx.var.server_name
    local LOCAL_TIME = ngx.localtime()
    local log_json_obj = {
                 client_ip = CLIENT_IP,
                 local_time = LOCAL_TIME,
                 server_name = SERVER_NAME,
                 user_agent = USER_AGENT,
                 attack_method = method,
                 req_url = url,
                 req_data = data,
                 rule_tag = ruletag,
              }
    local LOG_LINE = cjson.encode(log_json_obj)
    local LOG_NAME = LOG_PATH..'/'..ngx.today().."_waf.log"
    local file = io.open(LOG_NAME,"a")
    if file == nil then
        return
    end
    file:write(LOG_LINE.."\n")
    file:flush()
    file:close()
end

--WAF return
function waf_output()
    if config_waf_output == "redirect" then
        ngx.redirect(config_waf_redirect_url, 301)
    else
        ngx.header.content_type = "text/html"
        ngx.status = ngx.HTTP_FORBIDDEN
        ngx.say(config_output_html)
        ngx.exit(ngx.status)
    end
end

--String Split
function split(szFullString, szSeparator)
    local nFindStartIndex = 1
    local nSplitIndex = 1
    local nSplitArray = {}
    while true do
        local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)
        if not nFindLastIndex then
            nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))
            break
        end
        nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)
        nFindStartIndex = nFindLastIndex + string.len(szSeparator)
        nSplitIndex = nSplitIndex + 1
    end
    return nSplitArray
end

function returnjson(t)
    local cjson = require "cjson"
    local value=cjson.encode(t)
    ngx.header['Content-Type'] = 'application/json; charset=UTF-8'
    ngx.say(value)
end

function returnjsonobject(t)
    t = "{\"result\":true,\"data\":{\"areaList\":" .. t .. "},\"msg\":\"查询成功!\",\"rows\":\"\",\"total\":0,\"retcode\":\"\",\"footer\":\"\",\"errorMsg\":\"\",\"accode\":\"\",\"actimes\":\"\",\"bizid\":\"\",\"ext1\":\"\",\"ext2\":\"\",\"needmsgcode\":false}"
    ngx.header['Content-Type'] = 'application/json; charset=UTF-8'
    ngx.say(t)
end

function returnErrorJson(t)
    t = "{\"success\":true,\"data\":\"\",\"message\":\"".. t .. "\",\"errorCode\":\"S_NG_000000\"}"
    ngx.header['Content-Type'] = 'application/json; charset=UTF-8'
    ngx.say(t)
end

function waf_tokencheckoutput(ifiter)
    if ifiter=="i" then
        --接口
        log_record('接口',ngx.var.request_uri,"-","-")
        local t={retcode="-9999" }
        returnjson(t)
    else
        --h5页面
        log_record('h5页面',ngx.var.request_uri,"-","-")
        ngx.header.content_type = "text/html"
        ngx.say(config_tokencheck_outhtml)
        ngx.exit(200)
    end
end


function log_kafka()
    if config_logkafka_enable=="on" then
        local cjson = require("cjson")
        local utime= require("usertime")
        local CLIENT_IP = get_client_ip()
        local USER_AGENT = get_user_agent()
        local SERVER_NAME = ngx.var.server_name
        local LOCAL_TIME = utime.getmillisecond()
        local Method=ngx.req.get_method()
        local URL=ngx.var.uri
        local Params=""

        local filterurl = get_rule('logblack.rule')
        if URL~=nil then
            for _,rule in pairs(filterurl) do
                if rule ~="" and ngx.re.find(URL,rule,"jo") then
                    return
                end
            end
        end

        -- 不是客户端的请求 不处理
        if not string.find(USER_AGENT, "jygd") then
            return
        end

        --没有登陆的请求 不处理
        local usertoken = split(USER_AGENT,'_')[3]
        if usertoken ==nil then
            return
        end

        --处理userid
        local userId=""
        if string.len(usertoken) > 10 then
            userId=string.sub(usertoken,1,8)
        end

        if userId== "" then
            return
        end

        if string.upper(Method)== "GET" then
            Params=ngx.req.get_uri_args()
        end

        local log_json_obj = {
            ip = CLIENT_IP,
            datatime = LOCAL_TIME,
            url = URL,
            method=Method,
            userid=userId,
            params=Params
        }
        sendkafka(log_json_obj)
    end
end

function sendkafka(log_json)
    local cjson = require "cjson"
    local producer = require "resty.kafka.producer"
    -- 定义kafka broker地址，ip需要和kafka的host.name配置一致
    local broker_list = {
        { host = config_kafka_ip, port = config_kafka_port },
    }

    -- 转换json为字符串
    local message = cjson.encode(log_json);
    -- 定义kafka异步生产者
    local bp = producer:new(broker_list, { producer_type = "async" })
    -- 发送日志消息,send第二个参数key,用于kafka路由控制:
    -- key为nill(空)时，一段时间向同一partition写入数据
    -- 指定key，按照key的hash写入到对应的partition
    local ok, err = bp:send("logtras2", nil, message)

    if not ok then
        ngx.log(ngx.ERR, "kafka send err:", err)
        return
    end
end

--访问redis数据
function getcachedata(key)

    local config = {
        cache = { -- your connection name
            host = config_redis_ip,
            port = config_redis_port,
            pass =config_redis_pass,
            timeout = config_redis_timeout, -- watch out this value
            database = config_redis_database,
        }
    }

    local redis_factory = require('redisfac')(config)
    local ok, cache = redis_factory:spawn('cache')
    if not ok then
        redis_factory:destruct()
        return nil
    end

    local jsonstr , err = cache:get(key)
    redis_factory:destruct()
    return jsonstr;
end

function getTpagedata()
    local uri=ngx.var.uri
    if string.find("/webapi/templateindexapi/getIndex", uri) ~=nil then
        local params=ngx.req.get_uri_args()

        local pageid=""
        local flag=1

        for k,v in pairs(params) do
            if k == "pageid" then
                pageid=v
            end
            if k == "flag" then
                flag=v
            end
        end

        if flag == "1" and pageid ~= "" then
            crossdomain()
            local key= pageid .."GetTemplateFromRedisKey"

            local jsonstr2=get_from_cache(key);
            if jsonstr2 ~= nil then
                returnjsonobject(jsonstr2);
            else
                local jsonstr=getcachedata(key)
                set_to_cache(key, jsonstr, 60)
                if jsonstr ~= nil then
                    returnjsonobject(jsonstr)
                end
            end
        end
    end
end

function get_from_cache(key)
    local cache_ngx = ngx.shared.mycache
    local value = cache_ngx:get(key)
    return value
end

function set_to_cache(key, value, exptime)
    if not exptime then
        exptime = 0
    end
    local cache_ngx = ngx.shared.mycache
    local succ, err, forcible = cache_ngx:set(key, value, exptime)
    return succ
end

function crossdomain()
    ngx.header["Access-Control-Allow-Origin"] = "*"
    ngx.header["Access-Control-Allow-Methods"]="GET, POST"
    ngx.header["Access-Control-Allow-Headers"]="x-requested-with"
    ngx.header["Access-Control-Max-Age"]=1800
    ngx.header["Cache-Control"]="no-cache"

    ngx.header["Expires"]=-1
    ngx.header["Pragma"]="no-cache"

end