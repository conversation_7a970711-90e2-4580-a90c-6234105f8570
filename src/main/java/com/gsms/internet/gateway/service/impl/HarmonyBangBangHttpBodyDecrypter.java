package com.gsms.internet.gateway.service.impl;

import com.gsms.internet.gateway.bean.HKYZHttpBody;
import com.gsms.internet.gateway.service.HttpBodyDecrypter;

import com.gsms.internet.gateway.util.JSONUtils;
import com.gsms.internet.gateway.util.bangbang.Sm4Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 鸿蒙梆梆的解密器
 *
 * <AUTHOR>
 * @date 2024/8/7 09:36
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
public class HarmonyBangBangHttpBodyDecrypter implements HttpBodyDecrypter {

	/**
	 * 向量
	 */
    @Value("${harmony.bangbang.sm4Iv:}")
    private String sm4IvStr; 
    
    /**
     * key1
     */
    @Value("${harmony.bangbang.sm4key1:}")
    private String sm4key1; 
    
    /**
     * key2
     */
    @Value("${harmony.bangbang.sm4key2:}")
    private String sm4key2; 

    public HKYZHttpBody decrypt(String body, String clientCode, String methodName) throws Exception {
        HKYZHttpBody hkyzHttpBody = new HKYZHttpBody();
        String signBody = JSONUtils.parse(body).get("jsonData").asText();
        hkyzHttpBody.setSignBody(signBody);
        hkyzHttpBody.setBody(Sm4Utils.decrypt(signBody, sm4key2, sm4key1, sm4IvStr));
        return hkyzHttpBody;
    }
}
