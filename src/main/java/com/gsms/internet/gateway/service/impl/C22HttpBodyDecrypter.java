package com.gsms.internet.gateway.service.impl;

import com.gsms.internet.gateway.bean.HKYZHttpBody;
import com.gsms.internet.gateway.service.HttpBodyDecrypter;

import com.gsms.internet.gateway.util.AesUtils;
import com.gsms.internet.gateway.util.JSONUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 快应用渠道http-body解密
 *
 * <AUTHOR>
 * @date 2024/8/7 09:36
 * @company 昆仑数智科技有限责任公司
 */
@Component
@Slf4j
@RefreshScope
public class C22HttpBodyDecrypter implements HttpBodyDecrypter {

    @Value("${harmony.workKeyForC22:xxx}")
    private String workKeyForC22;

    public HKYZHttpBody decrypt(String body, String clientCode, String methodName) throws Exception {
        HKYZHttpBody hkyzHttpBody = new HKYZHttpBody();
        String signBody = JSONUtils.parse(body).get("jsonData").asText();
        body = AesUtils.decrypt(signBody, workKeyForC22);
        hkyzHttpBody.setBody(body);
        hkyzHttpBody.setSignBody(signBody);
        return hkyzHttpBody;
    }
}
