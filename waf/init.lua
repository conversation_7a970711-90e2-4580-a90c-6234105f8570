-- 参考：https://blog.csdn.net/Cafune_f/article/details/126431362
-- 参考：https://zhuanlan.zhihu.com/p/661423871
-- 初始化变量和声明相关方法
-- WAF Action
require 'config'
require 'lib'

local rulematch = ngx.re.find

local unescape = ngx.unescape_uri

-- 全局变量存储白名单规则
local white_url_exact_table = {}  -- 精确匹配的URL表
local white_url_regex_table = {}  -- 正则匹配的URL表

-- 剥离路径前缀的函数（支持动态前缀列表）
function strip_proxy_prefix(uri, prefixes)
    if not prefixes or #prefixes == 0 then
        return uri, nil
    end

    for _, prefix in pairs(prefixes) do
        if string.sub(uri, 1, string.len(prefix)) == prefix then
            -- 返回剥离前缀后的路径，保持以/开头
            local stripped = string.sub(uri, string.len(prefix))
            if string.sub(stripped, 1, 1) ~= "/" then
                stripped = "/" .. stripped
            end
            return stripped, prefix
        end
    end
    return uri, nil  -- 没有匹配的前缀，返回原始URI
end

-- 初始化白名单规则到内存
function init_white_url_rules()
    if config_white_url_check == "on" then
        local URL_WHITE_RULES = get_rule('whiteurl.rule')
        if URL_WHITE_RULES ~= nil then
            for _, rule in pairs(URL_WHITE_RULES) do
                if rule ~= "" then
                    -- 判断是否为正则表达式（包含特殊字符）
                    if string.find(rule, "[%^%$%(%)%[%]%*%+%?%|%\\%.]") then
                        -- 正则表达式规则
                        table.insert(white_url_regex_table, rule)
                    else
                        -- 精确匹配规则，使用table作为hash表实现O(1)查询
                        white_url_exact_table[rule] = true
                    end
                end
            end
            -- 计算精确匹配规则数量
            local exact_count = 0
            for _ in pairs(white_url_exact_table) do
                exact_count = exact_count + 1
            end
            ngx.log(ngx.INFO, "White URL rules loaded: ", #white_url_regex_table, " regex rules, ",
                    exact_count, " exact rules")
        end
    end
end

--allow white ip
function white_ip_check()
    if config_white_ip_check == "on" then
        local IP_WHITE_RULE = get_rule('whiteip.rule')
        local WHITE_IP = get_client_ip()
        if IP_WHITE_RULE ~= nil then
            for _, rule in pairs(IP_WHITE_RULE) do
                if rule ~= "" and rulematch(WHITE_IP, rule, "jo") then
                    --if config_black_ip_check_log == "on" then
                    --log_record('White_IP',ngx.var_request_uri,"_","_")
                    --end
                    return true
                end
            end
        end
    end
end

--deny black ip
function black_ip_check()
    if config_black_ip_check == "on" then
        local IP_BLACK_RULE = get_rule('blackip.rule')
        local BLACK_IP = get_client_ip()
        if IP_BLACK_RULE ~= nil then
            for _, rule in pairs(IP_BLACK_RULE) do
                if rule ~= "" and rulematch(BLACK_IP, rule, "jo") then
                    log_record('BlackList_IP', ngx.var_request_uri, "_", "_")
                    if config_waf_enable == "on" then
                        ngx.exit(403)
                        return true
                    end
                end
            end
        end
    end
end

--allow white url (优化版本，支持O(1)精确匹配、正则匹配和动态路径前缀剥离)
function white_url_check(proxy_prefixes)
    if config_white_url_check == "on" then
        -- ngx.var相关参数说明：https://www.cnblogs.com/chenpython123/p/10832393.html
        local REQ_URI = ngx.var.request_uri

        -- 剥离查询参数，只保留路径部分
        local path_only = string.match(REQ_URI, "([^%?]*)")

        -- 尝试剥离代理前缀（支持动态配置）
        local stripped_path, matched_prefix = path_only, nil

        local prefixes_to_use = proxy_prefixes

        stripped_path, matched_prefix = strip_proxy_prefix(path_only, prefixes_to_use)

        -- 检查剥离前缀后的路径
        if white_url_exact_table[stripped_path] then
            return true
        end

        -- 正则匹配再检查剥离前缀后的路径
        for _, rule in pairs(white_url_regex_table) do
            if rulematch(stripped_path, rule, "jo") then
                return true
            end
        end

    end
    return false
end

--deny cc attack
function cc_attack_check()
    if config_cc_check == "on" then
        local ATTACK_URI = ngx.var.uri
        local CC_TOKEN = get_client_ip() .. ATTACK_URI
        local limit = ngx.shared.limit
        CCcount = tonumber(string.match(config_cc_rate, '(.*)/'))
        CCseconds = tonumber(string.match(config_cc_rate, '/(.*)'))
        local req, _ = limit:get(CC_TOKEN)
        if req then
            if req > CCcount then
                log_record('CC_Attack', ngx.var.request_uri, "-", "-")
                if config_waf_enable == "on" then
                    ngx.exit(403)
                end
            else
                limit:incr(CC_TOKEN, 1)
            end
        else
            limit:set(CC_TOKEN, 1, CCseconds)
        end
    end
    return false
end

--deny cookie
function cookie_attack_check()
    if config_cookie_check == "on" then
        local COOKIE_RULES = get_rule('cookie.rule')
        local USER_COOKIE = ngx.var.http_cookie
        if USER_COOKIE ~= nil then
            for _, rule in pairs(COOKIE_RULES) do
                if rule ~= "" and rulematch(USER_COOKIE, rule, "jo") then
                    log_record('Deny_Cookie', ngx.var.request_uri, "-", rule)
                    if config_waf_enable == "on" then
                        waf_output()
                        return true
                    end
                end
            end
        end
    end
    return false
end

--deny url
function url_attack_check()
    if config_url_check == "on" then
        local URL_RULES = get_rule('url.rule')
        local REQ_URI = ngx.var.request_uri
        for _, rule in pairs(URL_RULES) do
            if rule ~= "" and rulematch(REQ_URI, rule, "jo") then
                log_record('Deny_URL', REQ_URI, "-", rule)
                if config_waf_enable == "on" then
                    waf_output()
                    return true
                end
            end
        end
    end
    return false
end

--deny url args
function url_args_attack_check()
    if config_url_args_check == "on" then
        local ARGS_RULES = get_rule('args.rule')
        for _, rule in pairs(ARGS_RULES) do
            local REQ_ARGS = ngx.req.get_uri_args()
            for key, val in pairs(REQ_ARGS) do
                if type(val) == 'table' then
                    ARGS_DATA = table.concat(val, " ")
                else
                    ARGS_DATA = val
                end
                if ARGS_DATA and type(ARGS_DATA) ~= "boolean" and rule ~= "" and rulematch(unescape(ARGS_DATA), rule, "jo") then
                    log_record('Deny_URL_Args', ngx.var.request_uri, "-", rule)
                    if config_waf_enable == "on" then
                        waf_output()
                        return true
                    end
                end
            end
        end
    end
    return false
end
--deny user agent
function user_agent_attack_check()
    if config_user_agent_check == "on" then
        local USER_AGENT_RULES = get_rule('useragent.rule')
        local USER_AGENT = ngx.var.http_user_agent
        if USER_AGENT ~= nil then
            for _, rule in pairs(USER_AGENT_RULES) do
                if rule ~= "" and rulematch(USER_AGENT, rule, "jo") then
                    log_record('Deny_USER_AGENT', ngx.var.request_uri, "-", rule)
                    if config_waf_enable == "on" then
                        waf_output()
                        return true
                    end
                end
            end
        end
    end
    return false
end
--allpath
function blackallpathcheck()
    if config_allpath_check == "on" then
        local PARH_RULES = get_rule('blackurl.rule')
        local USERPATH = ngx.var.scheme .. "://" .. ngx.var.server_name .. "/" .. ngx.var.uri
        if USERPATH ~= nil then
            for _, rule in pairs(PARH_RULES) do
                if rule ~= "" and rulematch(USERPATH, rule, "jo") then
                    log_record('Deny_URL', ngx.var.request_uri, "-", rule)
                    if config_waf_enable == "on" then
                        waf_output()
                        ngx.exit(403)
                    end
                end
            end
        end
    end
    return false;
end
--deny post
function post_attack_check()
    if config_post_check == "on" then
        local POST_RULES = get_rule('post.rule')
        for _, rule in pairs(POST_RULES) do
            local POST_ARGS = ngx.req.get_post_args()
        end
        return true
    end
    return false
end

--auto ip filter
function autofilterip()
    ip_bind_time = config_autofilterip_bind_time  --·â½ûIPÊ±¼ä
    ip_time_out = config_autofilterip_time_out    --Ö¸¶¨ip·ÃÎÊÆµÂÊÊ±¼ä¶Î
    connect_count = config_autofilterip_connect_count --Ö¸¶¨ip·ÃÎÊÆµÂÊ¼ÆÊý×î´óÖµ

    local config = {
        cache = { -- your connection name
            host = config_redis_ip,
            port = config_redis_port,
            pass = config_redis_pass,
            timeout = config_redis_timeout, -- watch out this value
            database = config_redis_database,
        }
    }
    --ngx.say("1")
    if config_autofilterip_check == "on" and config_waf_enable == "on" then
        local redis_factory = require('redisfac')(config) -- import config when construct
        local ok, cache = redis_factory:spawn('cache')
        if not ok then
            --ngx.say("2")
            redis_factory:destruct()
            return false
        end

        --²éÑ¯ipÊÇ·ñÔÚ·â½û¶ÎÄÚ£¬ÈôÔÚÔò·µ»Ø403´íÎó´úÂë
        --Òò·â½ûÊ±¼ä»á´óÓÚip¼ÇÂ¼Ê±¼ä£¬¹Ê´Ë´¦²»¶ÔipÊ±¼äkeyºÍ¼ÆÊýkey×ö´¦Àí
        is_bind, err = cache:get("bind_" .. ngx.var.remote_addr)
        if is_bind == "1" or is_bind == 1 then
            log_record('AutoBlackip', ngx.var.request_uri, "-", '')
            redis_factory:destruct()
            ngx.exit(403)
            return true
        end

        local filterurl = get_rule('filteripurl.rule')
        local path = ngx.var.uri
        if path ~= nil then
            for _, rule in pairs(filterurl) do
                if rule ~= "" and rulematch(path, rule, "jo") then
                    local start_time, err = cache:get("time_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri)
                    local ip_count, err = cache:get("count_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri)

                    --Èç¹ûip¼ÇÂ¼Ê±¼ä´óÓÚÖ¸¶¨Ê±¼ä¼ä¸ô»òÕß¼ÇÂ¼Ê±¼ä»òÕß²»´æÔÚipÊ±¼äkeyÔòÖØÖÃÊ±¼äkeyºÍ¼ÆÊýkey
                    --Èç¹ûipÊ±¼äkeyÐ¡ÓÚÊ±¼ä¼ä¸ô£¬Ôòip¼ÆÊý+1£¬ÇÒÈç¹ûip¼ÆÊý´óÓÚipÆµÂÊ¼ÆÊý£¬ÔòÉèÖÃipµÄ·â½ûkeyÎª1
                    --Í¬Ê±ÉèÖÃ·â½ûkeyµÄ¹ýÆÚÊ±¼äÎª·â½ûipµÄÊ±¼ä

                    if not start_time then
                        return false;
                    end

                    if start_time == ngx.null or (os.time() - start_time) > ip_time_out then
                        res, err = cache:set("time_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri, os.time())
                        res, err = cache:expire("time_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri, ip_time_out)
                        res, err = cache:set("count_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri, 1)
                        res, err = cache:expire("count_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri, ip_time_out)
                    else
                        ip_count = ip_count + 1
                        res, err = cache:incr("count_" .. ngx.var.remote_addr .. "_" .. ngx.var.request_uri)
                        if ip_count >= connect_count then
                            log_record('ADDAutoBlackip', ngx.var.remote_addr, "-", '')
                            res, err = cache:set("bind_" .. ngx.var.remote_addr, 1)
                            res, err = cache:expire("bind_" .. ngx.var.remote_addr, ip_bind_time)
                        end
                    end
                end
            end
        end
    end
    return false
end

--tokencheck
function tokencheck()
    local config = {
        cache = {
            host = config_redis_ip,
            port = config_redis_port,
            pass = config_redis_pass,
            timeout = config_redis_timeout,
            database = config_redis_database
        }
    }
    if config_tokencheck_check == "on" and config_waf_enable == "on" then
        local redis_factory = require('redisfac')(config) -- import config when construct
        local ok, cache = redis_factory:spawn('cache')
        if not ok then
            redis_factory:destruct()
            return false
        end

        local USER_AGENT_RULES = get_rule('tokenurlfilter.rule')
        local path = ngx.var.uri
        for _, rule in pairs(USER_AGENT_RULES) do
            local ifiter = split(rule, ";")[1]
            local rule2 = split(rule, ";")[2]
            --Æ¥Åäurl
            if rule2 ~= "" and rulematch(path, rule2, "jo") then
                local USER_AGENT = ngx.var.http_user_agent
                if string.len(USER_AGENT) == 0 then
                    log_record('tokenlost:USER_AGEN_LOST', ngx.var.request_uri, "-", rule2)
                    waf_tokencheckoutput(ifiter)
                    return true
                end
                local usertoken = split(USER_AGENT, '_')[3]
                if usertoken == nil then
                    log_record('tokenlost', ngx.var.uri, "-", rule2)
                    waf_tokencheckoutput(ifiter)
                    return true
                else
                    if string.len(usertoken) > 10 then
                        local userid = string.sub(usertoken, 1, 8)
                        local token = string.sub(usertoken, 9)
                        local accode, err = cache:get("accode_" .. userid)

                        if err == nil and string.len(accode) > 9 and string.sub(accode, 9) == token then
                            return false
                        else
                            log_record('accode:' .. accode .. ",token:" .. token, ngx.var.uri, "-", rule2)
                            waf_tokencheckoutput(ifiter)
                            return true
                        end
                    else
                        log_record('usertokenlenth<10', ngx.var.uri, "-", rule2)
                        waf_tokencheckoutput(ifiter)
                        return true
                    end
                end
            end
        end

    end
end

-- 在OpenResty启动时调用此函数初始化白名单规则
init_white_url_rules()

