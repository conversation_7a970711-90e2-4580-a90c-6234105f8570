# 通用业务配置（3.1.3变更）
# dev:http://172.30.255.5:30351/channel2capi
# sit:http://172.30.255.3:30351/channel2capi
# qas:http://172.30.255.4:30351/channel2capi
gsms:
  center:
    url: http://172.30.255.5:30351/channel2capi
  performance:
    url: http://172.30.255.16:30351
  aliyun:
    url: http://172.30.255.5:30351/channel2capi
  cooperation:
    url: http://172.30.255.43:30351/channel2pcapi
  openplatform:
    url: http://172.28.127.93:80
  openplatformcec:
    url: http://172.28.69.150:32105
  charge:
    url: http://172.28.69.152:30351
  obs-faceImage:
    centerName: member
    bucketName: gsmsb-dev-pri
  obs-interestsImage:
    centerName: member
    bucketName: gsmsb-dev-pri
  obs:
    prefix: /gsms-obs
  oss:
    prefix: oss-cn-beijing.aliyuncs.com
  login:
    key: oommmkkjeennqw99732!!@#$##




# 线程池配置
asyncUtil:
  get:
    outTime: 10
    listOutTime: 10

# jwt token配置
token:
  secret: reDTWH6DBE0A004A0FA0645E017DA3ADdev
  author: gsms
  refreshExpire: 1209600




# 优雅停机配置
server:
  shutdown: graceful
  undertow:
    no-request-timeout: -1
spring:
  redis:
    timeout: 3000
    password: KEUfEUSFMrm5NKD06j@g
    redisson:
      masterConnectionPoolSize: 100
      slaveConnectionPoolSize: 100
    host: *************
    port: 50103
  lifecycle:
    timeout-per-shutdown-phase: 15s
  application:
    name: internet-gateway
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false

    gateway:
      httpclient:
        response-timeout: PT10S
        connect-timeout: 5000
        pool:
          type: fixed
          acquire-timeout: 3000
          max-connections: 500
          eviction-interval: PT15S
          max-idle-time: PT30S
      routes:
        - id: ees-server
          uri: lb://ees-server
          predicates:
            - Path=/nyyz/**
          filters:
            - StripPrefix=1
       
#open-feign 相关配置
feign:
  client:
    config:
      default:
        connect-timeout: 1000
        read-timeout: 10000
        logger-level: headers
  httpclient:
    enabled: true
    max-connections: 200
    connection-timeout: 1000
    max-connections-per-route: 150
    time-to-live: 60
    time-to-live-unit: seconds

# prometheus和k8s健康检查
management:
  endpoint:
    health:
      probes:
        enabled: true
  endpoints:
    web:
      exposure:
        include: prometheus,health,threaddump,threadPool
  metrics:
    export:
      prometheus:
        enabled: true
      simple:
        enabled: false

#访问2.0的配置
cppei:
  login:
    #2.0登录总开关，switch=true，打开获取2.0的登录信息功能，switch=false，关闭获取2.0登录信息功能
    switch: true
    #渠道映射表，
    channelMap: '{
	    "C13":"C13",
        "C10":"C10",
        "C12":"C12",
        "C2930":"C10",
        "C14":"C10",
        "C2948":"C10",
        "C21":"C10"    
      }'
    #2.0服务地址
    host: http://*************:80
    #登录接口
    method: /gsms-a/v1/loginToCppei
    whereType: zsyCppei
    key: V2LMSEY7
    privateKey: MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAJutbLtxH-vANr-XqNWUQhcOvzX4GnlnvJ_FkPpoTK2nb-ARDhIJr4xdDeD1CsfiN_1M15BBYplPJTMHt6Fd09vTTSMM_v3Mz9fpaUP2PYJgAlDdkb9TYeVPsRLs56giY1hXIE5qSY5wZTCauXAUE-JRwDc4XYzNzof49OWoXIvHAgMBAAECgYB5gJuSmDI_v997HZUqTPJgKKRYLPIMu0hessX6ipj-F-abTPRcY67dsGr6noXxh8AHNIgyELYxzbHM6EML8L0-Y0JzP9quzGbEW9HtMzB4i9JQ2981cDX3s6cIk2tEk5ekAXDAVRXKtK07p4w-LZQcYpSk3-DZ0KM3XWJ48vAj8QJBAOpfeU5whAS6zu6xExxWD3VwiONsTokWhjSgyUk6hqYCEwI6no-bN5Res5h727S4f2bpBJ1SibyS7u_29dnqWpUCQQCqCvL5bzOwDQYA53OuTZwntHENImvMWXIYnhhRpZ-dXwAonsTQxU2CDDt1-V1FAoKBNlpzsiqwReC43VAu-ZHrAkAuB3gkfpZ-801GtYIiU4iPsP1BZPj7lW_gEB_YAOWG3utqyaZwX7SHsB2nedPJZ16mEGd2mkowvc6tVBwuowcNAkAexaR651jfPzwa5cfVf04HEWx0CAqiDH3NXq7f960O-aY5s6HCdjI0mqoYC2yJlI6xe3ia_18iT0L_sW1VUuzJAkEAxhOqyNQhO4tjc0Y9qqX1NIu0GQul6XBT3uBpvRkfLpDPpaX-mYhgH9enfagbbwLisF-QJecqd-ZL36DhqUgPDw

hkyy:
  host: hkyy-hkyympserver-soti-dev.soti-dev.svc:20105
  secret: az%4IEslTu1s#Ky6

#临时码失效时间
tmpcode:
  expireTime: 600

encrypt:
  routing:
    secret: DTWH6DBE0A004A0FA064507E017DA3AD
    appid: ********************************
harmony:
  keyManagerId: KMT7SUSv7BG5CQJCrBIAAg==
  appId: com.sunboxsoft.charge.institute
  workKey: jfhrnms6781jhdbc
  importKey-pub: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAw9I2IQ91iWUiHyJEvxicK8icovamkeEzQlH7UOWCCGJorfLyDBSfUoijH2eCSfsDW0QF6V-32Lh0dFL5z6bDkcO12P1ZHFcZP8rAs4G2eTdPrn_tKwKF_V8oMevNiNcsXEqhfBGWtlxn2VT3nKG6dOYwIGeTlVx7DvZLs0h6vYnFZujoqscdIu-YLOdp6qP1O56hEcBSlJUnSS23ID1RmG5bA7eycnX2mABb61rE8eTg6011XoGd6HJHJ7whPBCIfGT8l5hoicQbfkPNBzovP7L5761FRDoAbEZoFNuuhq4YhP9Wj1RM7oRMG7_hVSiTZTwnd_2OkdyO6NQgOCMTmwIDAQAB
  importKey-pri: ********************************BKcwggSjAgEAAoIBAQDD0jYhD3WJZSIfIkS_GJwryJyi9qaR4TNCUftQ5YIIYmit8vIMFJ9SiKMfZ4JJ-wNbRAXpX7fYuHR0UvnPpsORw7XY_VkcVxk_ysCzgbZ5N0-uf-0rAoX9Xygx682I1yxcSqF8EZa2XGfZVPecobp05jAgZ5OVXHsO9kuzSHq9icVm6Oiqxx0i75gs52nqo_U7nqERwFKUlSdJLbcgPVGYblsDt7JydfaYAFvrWsTx5ODrTXVegZ3ockcnvCE8EIh8ZPyXmGiJxBt-Q80HOi8_svnvrUVEOgBsRmgU266GrhiE_1aPVEzuhEwbv-FVKJNlPCd3_Y6R3I7o1CA4IxObAgMBAAECggEAWdXsg0pGT9wXxVTBWTH_I48LXS_uxuobzAc0Vq6T8yjCgVr9dFFZrlGFFHk2Kp5_sbUJSUFiZIhq1JrkhEErY4n3uS6xXPEQYlWwBiox_nZDlfiWY67qrYykMhLN3kNo1IZggzzGea727ZuvZSpFEjAwUA10hYdB5WFU5VERDjaC6UG-dP_LSZ_hyHLOHjpw9-psn56ifeWgidhVCk8Q_J4ouXSYSV6or-_tC7-Hromkxv-PUSmtyNDvARFRAeXll-UADI0080tW1wI_i4Ggw-z7I_BDLFQ79o4RSPGQHtVq4JNly4zWYsweWQkigZ-62VQxnzKSQMGuEIXWWpkL4QKBgQDiK8IZF4wy1McDCEpNbYc9igDticqfnikmP2_53_mfBiWwPavZlje1mYGlVSvIUuKk1fCO-C6N6ex2cFZqVszm6N912Ey8XmMAW-fEVHzo8cKRwkpVX3nGwoOFGNIkWcP_bcH05-sXEhi6UFWz9og3kI98e6r-AAdfxJnajcqYswKBgQDdpb-zkqHJRgrsOkC5GPVrxkttul9dRGFs2ruwLyYxIpi6GmosjyTYiCu9E9qpvUraYaad4hnr41ri3piHaGX7v2aaD-sNltkgbWXfRKts-0gQb1zZIzO2_ch5PdkQybTMj8Um8jteSpyjTsypN3CwoI5mH-3Ov756K6Lbp6j9eQKBgQCzojWjYESGCuGV0vvQy3Fiuz1JbOt2I1RjgHcba30DhQpiFFltlBXjMmz6N89yaAKDETjjFNNE-8pHt6mrBA3ySHtTSdyIsB7JpjVKhiA_k-UJh9sqO46pAs0a5T_ihzlYooAuWBUsWb8yG6Q2L1S5bSPJpx-EZdl1gtz_7SZAaQKBgG3rRg-S92LutnoCRZjF1OgW1c9b7vE8-SfXrd6o80LIyn74nJDuiDJODYreLsGg219DsDJ6L6q4_JioFXKJhpH7kLF0lXgOyCz5s9yunGkWkN19rMkOWRY181aVInHd5ijQX9B5rpGLZPTkHkEI-nzNkvD4gqHWm2rQ-xn9oqPZAoGAcxx47JObFxZvqa9jealK_57SdirA4Fdfi-vdd5nXxlhHF1z3mLzqRWqawk2MzfjsifGgXMvLOmpWfgklpK7LVMpaYh0M3J3x8lyKPd9rHvQh5B8CC8psKcpPeCCjqk2c8PpodYczV8XqHeAEtionb07Fi_VRWPJxA5QWksABDSc

  bangbang:
    sm4Iv: b3fA6d4K20C24Z9a
    sm4key1: urOXT3FULoiyFT1LyvFvSA==
    sm4key2: zKBx/wEFgydLnEp1M9q8TQ==
  workKeyForC22: jfh9nms6781jhdb9
system:
  department:
    key:
      configs:
        C13:
          mechanismName: "支付宝小程序"
          keyMd5: "sfhgQer59tyg"
          type: 1
          sourceCode: 5
        C10:
          mechanismName: "APP"
          keyMd5: "sdfgzPIkfg60po"
          type: 1
          sourceCode: 2
        C12:
          mechanismName: "微信小程序"
          keyMd5: "CKV2JR04"
          type: 1
          sourceCode: 4
        C23:
          mechanismName: "元服务"
          keyMd5: "SpRv9jSQ2y9kc8z3pG"
          type: 1
          sourceCode: 46
# 限流配置,新增和修改以及删除以下参数，均需要重启应用
# rate：令牌数
# seconds：秒数
# 支持header字段限流和uri限流
rate:
  limit:
    rules:
      - key: clientCode
        value: HKYY
        rate: 1
        seconds: 1
        type: header




# token白名单url(v3.1.3有变更)
ignore:
  token:
    uris:
      - uri: /user/login
        authType: 1
      - uri: /user/member/migration
        authType: 1
      - uri: /user/refreshToken
        authType: 1
      - uri: /user/agreement
        authType: 2
      - uri: /user/initNonLoginRealPerson
        authType: 1
      - uri: /user/nonLoginRealPersonIdentify
        authType: 1
      - uri: /content/messageCode/noLoginSend
        authType: 1
      - uri: /content/getVerifySlideImage
        authType: 1
      - uri: /user/setLoginPassword
        authType: 1
      - uri: /user/realPersonIdentifyLogin
        authType: 1
      - uri: /user/getDesensitizedPhone
        authType: 1
      - uri: /secret/getImportKey
        authType: 1
      - uri: /secret/getWorkKey
        authType: 1
      - uri: /invoice/nonLogin/createInvoice
        authType: 1
      - uri: /user/nonLogin/getAlipayUserOpenId
        authType: 1
      - uri: /user/nonLogin/getWechatUserOpenId
        authType: 1
      - uri: /user/initWechatFaceLoginByCode
        authType: 1
      - uri: /oilstation/station/list
        authType: 2


# 定制化校验 (v3.1.3.4有变更)
uri:
  auth:
    uris:
      - uri: /user/login
        authClientCode: C2.0,HKYY,C10,C12,C13,C14,C21,C22,C23
        authBeanName: LoginAuthServiceImpl
      - uri: /user/member/queryNo
        authClientCode: C2.0,HKYY
      - uri: /user/member/migration
        authClientCode: C2.0
        authBeanName: RemoveAuthServiceImpl
      - uri: /user/initNonLoginRealPerson
        authClientCode: C2.0,C10
      - uri: /user/nonLoginRealPersonIdentify
        authClientCode: C2.0
      - uri: /content/messageCode/noLoginSend
        authClientCode: HKYY,C10,C12,C14,C22
      - uri: /account/palmPay/distributeActivityCoupon
        authClientCode: HKYY
      - uri: /account/palmPay/distributeAccountCoupon
        authClientCode: HKYY
      - uri: /order/consume/getPayMethods
        authClientCode: HKYY
      - uri: /user/setLoginPassword
        authClientCode: C10
      - uri: /user/realPersonIdentifyLogin
        authClientCode: C10
      - uri: /user/getPhone
        authClientCode: HKYY

# 路由信息 (v3.1.3有变更)
error-code:
  msg: {
    "-1": "系统异常，程序猿正在努力解决",
    "400": "系统异常，程序猿正在努力解决",
    "500": "系统异常，程序猿正在努力解决",
    "B_B00_000001": "当前操作存在风险",
    "B_B00_000002": "网络不给力，请您稍后再试",
    "B_B00_000003": "当前操作存在风险",
    "B_B01_001018": "系统异常，程序猿正在努力解决",
    "B_B01_001019": "当前油站暂未开通此服务，请联系油站工作人员",
    "B_B01_003008": "优惠券无效或不满足使用规则，请更换优惠券",
    "B_B01_003009": "支付金额不能小于券面额，请更换优惠券",
    "B_B02_003202": "您已完成实名认证，无需重复认证",
    "B_B02_003204": "系统异常，程序猿正在努力解决",
    "B_B02_003205": "请先录入身份证相关信息才可进行实人认证",
    "B_B02_004008": "绑定车辆数量超出限制",
    "B_B02_004009": "请先解除无感支付再进行车辆删除",
    "B_B02_008011": "当前群组会员不是待激活状态，无法激活",
    "B_B02_008013": "当前状态不能提交申请",
    "B_B02_008020": "群组会员信息不存在",
    "B_B02_008021": "您已加入群组，无法重复加入",
    "B_B02_008022": "您已被移出该群组",
    "B_B02_008023": "该群组邀请已过期",
    "B_B02_008029": "会员信息不存在,请您稍后再试！",
    "B_B02_008030": "未找到当前群组信息",
    "B_B02_008031": "当前群组30天内不能重复申请",
    "B_B05_000003": "系统异常，程序猿正在努力解决",
    "B_B05_000004": "正在下单，请您耐心等待",
    "B_B05_000006": "系统异常，程序猿正在努力解决",
    "B_B05_000007": "系统异常，程序猿正在努力解决",
    "B_B05_000009": "当前油站暂未开通此服务，如您已到站，请到收银台支付",
    "B_B05_000010": "订单发起支付后2分钟内无法取消，请稍后再试",
    "B_B05_000011": "订单重复，请勿多次提交",
    "B_B05_000012": "系统异常，程序猿正在努力解决",
    "B_B05_000017": "系统异常，程序猿正在努力解决",
    "B_B05_000022": "当前油站暂未开通此服务，如您已到站，请到收银台支付",
    "B_B05_000024": "网络不给力，请您稍后再试",
    "B_B05_000025": "正在为您加油，无法取消订单，如您未到站加油，请联系客服解决",
    "B_B05_000028": "系统异常，程序猿正在努力解决",
    "B_B05_000029": "网络不给力，请您稍后再试",
    "B_B05_000032": "系统异常，程序猿正在努力解决",
    "B_B05_000033": "系统异常，程序猿正在努力解决",
    "B_B05_000050": "正在为您加油，无法取消订单，如您未到站加油，请联系客服解决",
    "B_B05_200018": "您有一笔待支付订单，请立即支付",
    "B_B07_001006": "系统异常，程序猿正在努力解决",
    "B_B07_001007": "系统异常，程序猿正在努力解决",
    "B_B07_010000": "系统异常，程序猿正在努力解决",
    "B_B07_102001": "系统异常，程序猿正在努力解决",
    "B_B07_1020015": "系统异常，程序猿正在努力解决",
    "B_B07_102007": "存在可用余额或冻结金额，请处理后在操作",
    "B_B07_102012": "当前操作存在风险",
    "B_B07_102022": "系统异常，程序猿正在努力解决",
    "B_B07_102023": "注销次数已达上限",
    "B_B07_102025": "注销次数已达上限",
    "B_B07_103005": "系统异常，程序猿正在努力解决",
    "B_B07_104003": "系统异常，程序猿正在努力解决",
    "B_B07_105003": "网络不给力，请您稍后再试",
    "B_B07_106004": "系统异常，程序猿正在努力解决",
    "B_B07_106005": "系统异常，程序猿正在努力解决",
    "B_B07_118003": "系统异常，程序猿正在努力解决",
    "B_B07_118006": "当前常用地与您要修改的相同，无需修改",
    "B_B07_118008": "网络不给力，请您稍后再试",
    "B_B07_118009": "系统异常，程序猿正在努力解决",
    "B_B07_118011": "系统异常，程序猿正在努力解决",
    "B_B07_118012": "常用地修改次数已达本年度上限",
    "B_B07_118014": "存在冻结金额,请处理后再操作",
    "B_B07_207003": "系统异常，程序猿正在努力解决",
    "B_B07_207005": "系统异常，程序猿正在努力解决",
    "B_B07_207006": "密码错误，请重新输入",
    "B_B07_212002": "系统异常，程序猿正在努力解决",
    "B_B07_212006": "本次绑卡不符合业务规则",
    "B_B07_212007": "绑卡数量不能超过1张，请先进行解绑",
    "B_B07_212012": "当前加油卡已被您绑定，无需重复绑定",
    "B_B07_212013": "系统异常，程序猿正在努力解决",
    "B_B07_214002": "系统异常，程序猿正在努力解决",
    "B_B07_214003": "系统异常，程序猿正在努力解决",
    "B_B07_215002": "系统异常，程序猿正在努力解决",
    "B_B07_216005": "您还未设置支付密码，请设置后再操作",
    "B_B07_217004": "系统异常，程序猿正在努力解决",
    "B_B07_2240001": "系统异常，程序猿正在努力解决",
    "B_B07_2240002": "系统异常，程序猿正在努力解决",
    "B_B07_2240004": "系统异常，程序猿正在努力解决",
    "B_B07_2240005": "系统异常，程序猿正在努力解决",
    "B_B07_250001": "系统异常，程序猿正在努力解决",
    "B_B07_301004": "昆仑e享卡余额无法超过5000，充值失败",
    "B_B07_301007": "该笔订单存在风险，暂时无法进行充值",
    "B_B07_303005": "支付密码错误，请重新输入",
    "B_B07_313001": "系统异常，程序猿正在努力解决",
    "B_B07_314002": "未查询到订单",
    "B_B11_002028": "当前油站暂未开通此服务，请联系油站工作人员",
    "B_B11_002029": "当前油站暂未开通此服务，请联系油站工作人员",
    "B_B11_002033": "当前油站暂未开通此服务，请联系油站工作人员",
    "B_B12_007": "验证码发送异常，程序猿正在努力解决",
    "B_B12_021": "验证码发送异常，程序猿正在努力解决",
    "B_B12_036": "系统异常，程序猿正在努力解决",
    "B_B12_038": "验证码发送频率过高,请您稍后再试",
    "B_B12_049": "验证码发送异常，程序猿正在努力解决",
    "B_B12_050": "验证码发送异常，程序猿正在努力解决",
    "B_B12_051": "验证码已过期，请重新获取",
    "B_B12_052": "验证码不正确，请重新输入",
    "B_B18_005023": "请选择省份信息",
    "B_B18_009002": "系统异常，程序猿正在努力解决",
    "B_B20_100000": "开票失败，请拨打956100客服中心进行处理",
    "BIZ_ERROR": "会员升级失败，请前往中油好客e站App变更电子卡归属地",
    "P_B01_999999": "系统异常，程序猿正在努力解决",
    "P_B02_003201": "系统异常，程序猿正在努力解决",
    "P_B02_003202": "注销处理中，请勿重新提交",
    "P_B02_003203": "您的实人信息已登记至其他账号中，当前账号无法进行认证",
    "P_B02_004007": "该车牌号已存在",
    "P_B02_901001": "开通主账户和权益账户失败，请您稍后再试",
    "P_B02_901002": "开通账户失败，请您稍后再试",
    "P_B02_901003": "电子卡资金积分迁移失败，请您稍后再试",
    "P_B02_901004": "绑定加油卡失败，请您稍后再试",
    "P_B02_901005": "注销移动支付失败，无法升级，请您稍后再试",
    "P_B02_901006": "您目前有多个电子卡，无法进行迁移",
    "P_B02_901007": "未查到电子卡信息，无法进行迁移",
    "P_B02_901009": "您的实体卡数量多于5张，请先解绑至5张以下再进行升级",
    "P_B02_901010": "该电子卡状态异常，无法进行迁移",
    "P_B02_901015": "您存在冻结金额，请先解冻后再尝试升级",
    "P_B05_000001": "系统异常，程序猿正在努力解决",
    "P_B05_000002": "系统异常，程序猿正在努力解决",
    "P_B05_999999": "系统异常，程序猿正在努力解决",
    "P_B07_000009": "系统异常，程序猿正在努力解决",
    "P_B07_107004": "系统异常，程序猿正在努力解决",
    "P_B07_109004": "优惠券不存在",
    "P_B07_205001": "支付密码不可为空",
    "P_B07_207002": "系统异常，程序猿正在努力解决",
    "P_B07_208004": "系统异常，程序猿正在努力解决",
    "P_B07_210002": "系统异常，程序猿正在努力解决",
    "P_B07_211001": "系统异常，程序猿正在努力解决",
    "P_B07_212006": "系统异常，程序猿正在努力解决",
    "P_B07_212007": "系统异常，程序猿正在努力解决",
    "P_B07_301004": "系统异常，程序猿正在努力解决",
    "P_B07_701000": "网络不给力，请稍后再试",
    "P_B07_701001": "网络不给力，请稍后再试",
    "P_B07_701002": "网络不给力，请稍后再试",
    "P_B07_701003": "网络不给力，请稍后再试",
    "P_B07_701010": "网络不给力，请稍后再试",
    "P_B07_701011": "网络不给力，请稍后再试",
    "P_B07_701012": "网络不给力，请稍后再试",
    "P_B07_702000": "网络不给力，请稍后再试",
    "P_B07_703000": "网络不给力，请稍后再试",
    "P_B07_704000": "网络不给力，请稍后再试",
    "P_B07_704001": "网络不给力，请稍后再试",
    "P_B07_704002": "网络不给力，请稍后再试",
    "P_B07_704003": "网络不给力，请稍后再试",
    "P_B07_704004": "网络不给力，请稍后再试",
    "P_B07_704005": "网络不给力，请稍后再试",
    "P_B07_704006": "网络不给力，请稍后再试",
    "P_B07_705000": "网络不给力，请稍后再试",
    "P_B07_705001": "网络不给力，请稍后再试",
    "P_B07_705002": "网络不给力，请稍后再试",
    "P_B07_705003": "网络不给力，请稍后再试",
    "P_B07_705004": "网络不给力，请稍后再试",
    "P_B07_705005": "网络不给力，请稍后再试",
    "P_B07_705006": "网络不给力，请稍后再试",
    "P_B07_705007": "网络不给力，请稍后再试",
    "P_B07_705008": "网络不给力，请稍后再试",
    "P_B07_706000": "网络不给力，请稍后再试",
    "P_B07_708000": "系统异常，程序猿正在努力解决",
    "P_B07_708001": "系统异常，程序猿正在努力解决",
    "P_B11_99999": "系统异常，程序猿正在努力解决",
    "P_B21_000001": "系统异常，程序猿正在努力解决",
    "PAY_ERROR_001": "支付异常，程序猿正在努力解决",
    "PAY_ERROR_002": "系统异常，程序猿正在努力解决",
    "PAY_ERROR_003": "为保障您的交易安全，请进行人脸识别",
    "S_B09_001": "网络不给力，请您稍后再试",
    "S_B09_002": "网络不给力，请您稍后再试",
    "S_B09_003": "文件格式不正确，无法上传",
    "B_B07_301009": "今日充值次数已达上限，请明日重试",
    "S_C20_000000": "系统异常，程序猿正在努力解决"
  }
