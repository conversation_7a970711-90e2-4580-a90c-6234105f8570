package com.gsms.internet.gateway.util.bangbang;

import java.security.MessageDigest;
import java.util.Base64;

public class Sm4Utils {

	/**
	 * sm4 解密
	 * @param cipherText
	 * @return
	 * @throws Exception
	 */
	public static String decrypt(String cipherText, String sm4key2, String sm4key1, String sm4IvStr) throws Exception {

		byte[] sm4key1s = Base64.getDecoder().decode(sm4key1);
		byte[] sm4key2s = Base64.getDecoder().decode(sm4key2);
		
		//第一次解密
		String outOne = SM4Crypter.decrypt(cipherText, sm4key2s, sm4IvStr);

		cipherText = outOne.substring(0,outOne.length()-16);

		String getIV = outOne.substring(outOne.length()-16);//截取最后的16位IV值

		//第二次解密
		String plainText = SM4Crypter.decrypt(cipherText, sm4key1s, getIV);
		
		checkCk(plainText, "");

		return plainText;

	}

	

	/**
	 * 默认校验的是json格式的checkcode值
	 * @param msg
	 * @param key_str 暂时没使用
	 * @return
	 */
	public static int checkCk(String msg, String key_tr) {

		int index = msg.indexOf(",\"checkcode\"");
		String msgCk = msg.substring(index + 14, index + 14 + 64);
		String endStr = msg.substring(index + 14 + 65, msg.length());
		String newMsg = msg.substring(0, index) + endStr;
		String newCk = getSHA256(newMsg);
		if (msgCk.equals(newCk)) {
			return 1;
		} else {
			throw new RuntimeException("参数校验失败");
		}
	}
	
	public static String getSHA256(String str){
		MessageDigest messageDigest;
		String encodeStr = "";
		try {
			messageDigest = MessageDigest.getInstance("SHA-256");
			messageDigest.update(str.getBytes("UTF-8"));
			encodeStr = byte2Hex(messageDigest.digest());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return encodeStr;
	}
	
	private static String byte2Hex(byte[] bytes){
		StringBuffer stringBuffer = new StringBuffer();
		String temp = null;
		for (int i=0;i<bytes.length;i++){
			temp = Integer.toHexString(bytes[i] & 0xFF);
			if (temp.length()==1){
				//1得到一位的进行补0操作
				stringBuffer.append("0");
			}
			stringBuffer.append(temp);
		}
		return stringBuffer.toString();
	}
}
